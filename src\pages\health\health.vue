<template>
  <view class="health-container">
    <!-- 状态栏 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <view class="page-title">
          <text class="title-text">健康监测</text>
          <text class="subtitle-text">智能分析 • 实时评估</text>
        </view>
        <view class="health-status" :class="overallHealthStatus.class">
          <view class="status-icon">{{ overallHealthStatus.icon }}</view>
        </view>
      </view>
    </view>

    <!-- 健康评分卡片 -->
    <view class="health-score-card">
      <view class="score-background">
        <view class="score-circle">
          <view class="score-progress" :style="{ transform: `rotate(${healthScore * 3.6}deg)` }"></view>
          <view class="score-content">
            <text class="score-number">{{ healthScore }}</text>
            <text class="score-label">健康分数</text>
          </view>
        </view>
      </view>
      <view class="score-description">
        <text class="score-status">{{ getHealthStatusText(healthScore) }}</text>
        <text class="score-advice">{{ getHealthAdvice(healthScore) }}</text>
      </view>
    </view>

    <!-- 健康指标网格 -->
    <view class="health-metrics">
      <view class="metrics-header">
        <text class="metrics-title">关键指标</text>
        <text class="metrics-time">{{ formatTime(Date.now()) }}</text>
      </view>
      <view class="metrics-grid">
        <view class="metric-card exposure">
          <view class="metric-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
          </view>
          <view class="metric-content">
            <text class="metric-label">辐射暴露</text>
            <text class="metric-value">{{ exposureLevel.toFixed(2) }}</text>
            <text class="metric-unit">%</text>
          </view>
          <view class="metric-trend" :class="exposureTrend.class">
            <text class="trend-icon">{{ exposureTrend.icon }}</text>
          </view>
        </view>

        <view class="metric-card dose">
          <view class="metric-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
            </svg>
          </view>
          <view class="metric-content">
            <text class="metric-label">累积剂量</text>
            <text class="metric-value">{{ radiationState.currentData.doseSum.toFixed(3) }}</text>
            <text class="metric-unit">μSv</text>
          </view>
          <view class="metric-trend" :class="doseTrend.class">
            <text class="trend-icon">{{ doseTrend.icon }}</text>
          </view>
        </view>

        <view class="metric-card duration">
          <view class="metric-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 6v6l4 2"></path>
            </svg>
          </view>
          <view class="metric-content">
            <text class="metric-label">监测时长</text>
            <text class="metric-value">{{ monitoringDuration.hours }}</text>
            <text class="metric-unit">小时</text>
          </view>
          <view class="metric-trend stable">
            <text class="trend-icon">📊</text>
          </view>
        </view>

        <view class="metric-card risk">
          <view class="metric-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
              <line x1="12" y1="9" x2="12" y2="13"></line>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
          </view>
          <view class="metric-content">
            <text class="metric-label">风险等级</text>
            <text class="metric-value">{{ riskLevel.level }}</text>
            <text class="metric-unit">级</text>
          </view>
          <view class="metric-trend" :class="riskLevel.class">
            <text class="trend-icon">{{ riskLevel.icon }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 健康建议卡片 -->
    <view class="health-advice-section">
      <view class="advice-header">
        <text class="advice-title">个性化建议</text>
        <view class="ai-badge">
          <text>AI分析</text>
        </view>
      </view>
      <view class="advice-list">
        <view 
          v-for="(advice, index) in healthAdviceList" 
          :key="index"
          class="advice-item"
          :class="advice.type"
        >
          <view class="advice-icon">
            <text>{{ advice.icon }}</text>
          </view>
          <view class="advice-content">
            <text class="advice-text">{{ advice.text }}</text>
            <text class="advice-detail">{{ advice.detail }}</text>
          </view>
          <view class="advice-priority" :class="advice.priority">
            <text>{{ advice.priorityText }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 历史趋势图表 -->
    <view class="trend-chart-section">
      <view class="chart-header">
        <text class="chart-title">健康趋势</text>
        <view class="chart-period">
          <text class="period-label">近7天</text>
        </view>
      </view>
      <view class="chart-container">
        <canvas class="trend-chart" canvas-id="healthTrendChart" :style="{ width: chartWidth + 'px', height: '200px' }"></canvas>
        <view v-if="isLoadingChart" class="chart-loading">
          <view class="loading-spinner"></view>
          <text>分析中...</text>
        </view>
      </view>
      <view class="chart-legend">
        <view class="legend-item">
          <view class="legend-color health"></view>
          <text>健康分数</text>
        </view>
        <view class="legend-item">
          <view class="legend-color exposure"></view>
          <text>暴露水平</text>
        </view>
      </view>
    </view>

    <!-- 快速操作 -->
    <view class="quick-actions">
      <view class="action-button report" @tap="generateHealthReport">
        <view class="action-icon">📋</view>
        <text class="action-text">健康报告</text>
      </view>
      <view class="action-button reminder" @tap="setHealthReminder">
        <view class="action-icon">⏰</view>
        <text class="action-text">设置提醒</text>
      </view>
      <view class="action-button export" @tap="exportHealthData">
        <view class="action-icon">📤</view>
        <text class="action-text">导出数据</text>
      </view>
    </view>

    <!-- 全局统一的底部导航栏 -->
    <view class="modern-bottom-nav">
      <view class="nav-background"></view>
      <view class="nav-content">
        <view class="nav-tab" @tap="navigateTo('dashboard')">
          <view class="tab-icon-container">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="9" y1="9" x2="9" y2="15"></line>
              <line x1="15" y1="9" x2="15" y2="15"></line>
            </svg>
          </view>
          <text class="tab-label">仪表盘</text>
        </view>

        <view class="nav-tab" @tap="navigateTo('charts')">
          <view class="tab-icon-container">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
            </svg>
          </view>
          <text class="tab-label">数据</text>
        </view>

        <view class="nav-tab active" @tap="navigateTo('health')">
          <view class="tab-icon-container">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"></path>
            </svg>
          </view>
          <text class="tab-label">健康</text>
        </view>

        <view class="nav-tab" @tap="navigateTo('map')">
          <view class="tab-icon-container">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </view>
          <text class="tab-label">地图</text>
        </view>

        <view class="nav-tab" @tap="navigateTo('settings')">
          <view class="tab-icon-container">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-6.5l-4.24 4.24M7.76 7.76L3.52 3.52m12.96 12.96l-4.24-4.24M7.76 16.24l-4.24 4.24"></path>
            </svg>
          </view>
          <text class="tab-label">设置</text>
        </view>
      </view>
    </view>
    <!-- 底部导航栏 -->
    <BottomNavigation currentPage="health" />
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState } from '../../utils/dataStore.js'
import BottomNavigation from '../../components/BottomNavigation.vue'

// 响应式数据
const healthScore = ref(85)
const exposureLevel = ref(15.6)
const chartWidth = ref(350)
const isLoadingChart = ref(false)

// 监测时长
const monitoringDuration = computed(() => {
  const startTime = Date.now() - (7 * 24 * 60 * 60 * 1000) // 假设7天前开始监测
  const duration = Date.now() - startTime
  const hours = Math.floor(duration / (1000 * 60 * 60))
  return { hours }
})

// 整体健康状态
const overallHealthStatus = computed(() => {
  if (healthScore.value >= 90) {
    return { class: 'excellent', icon: '💚' }
  } else if (healthScore.value >= 80) {
    return { class: 'good', icon: '💙' }
  } else if (healthScore.value >= 70) {
    return { class: 'warning', icon: '💛' }
  } else {
    return { class: 'danger', icon: '❤️' }
  }
})

// 暴露趋势
const exposureTrend = computed(() => {
  if (exposureLevel.value < 10) {
    return { class: 'down', icon: '📉' }
  } else if (exposureLevel.value > 20) {
    return { class: 'up', icon: '📈' }
  } else {
    return { class: 'stable', icon: '➖' }
  }
})

// 剂量趋势
const doseTrend = computed(() => {
  const current = radiationState.currentData.doseSum
  if (current < 0.1) {
    return { class: 'down', icon: '📉' }
  } else if (current > 0.5) {
    return { class: 'up', icon: '📈' }
  } else {
    return { class: 'stable', icon: '➖' }
  }
})

// 风险等级
const riskLevel = computed(() => {
  const dose = radiationState.currentData.doseRate
  if (dose < 0.1) {
    return { level: 1, class: 'down', icon: '✅' }
  } else if (dose < 0.5) {
    return { level: 2, class: 'stable', icon: '⚠️' }
  } else if (dose < 1.0) {
    return { level: 3, class: 'up', icon: '🔸' }
  } else {
    return { level: 4, class: 'up', icon: '🔴' }
  }
})

// 健康建议列表
const healthAdviceList = computed(() => {
  const advice = []
  
  if (exposureLevel.value > 15) {
    advice.push({
      type: 'important',
      icon: '🛡️',
      text: '注意辐射防护',
      detail: '当前暴露水平偏高，建议减少在高辐射区域的停留时间',
      priority: 'high',
      priorityText: '重要'
    })
  }
  
  if (radiationState.currentData.doseSum > 0.3) {
    advice.push({
      type: 'suggestion',
      icon: '💊',
      text: '补充抗氧化剂',
      detail: '建议摄入富含维生素C、E的食物，增强身体抗氧化能力',
      priority: 'medium',
      priorityText: '建议'
    })
  }
  
  advice.push({
    type: 'info',
    icon: '💤',
    text: '保证充足睡眠',
    detail: '充足的睡眠有助于细胞修复和免疫系统恢复',
    priority: 'medium',
    priorityText: '建议'
  })
  
  if (healthScore.value < 80) {
    advice.push({
      type: 'important',
      icon: '🏃‍♂️',
      text: '增加体育锻炼',
      detail: '适度运动可以提高身体免疫力和整体健康水平',
      priority: 'high',
      priorityText: '重要'
    })
  }
  
  return advice
})

// 工具函数
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

const getHealthStatusText = (score) => {
  if (score >= 90) return '优秀状态'
  if (score >= 80) return '良好状态'
  if (score >= 70) return '一般状态'
  return '需要关注'
}

const getHealthAdvice = (score) => {
  if (score >= 90) return '继续保持当前的健康生活方式，定期检查各项指标'
  if (score >= 80) return '整体状况良好，注意辐射防护和规律作息'
  if (score >= 70) return '建议加强锻炼，改善饮食习惯，增强身体抵抗力'
  return '请及时咨询医生，制定个性化的健康改善计划'
}

// 统一的导航函数
const navigateTo = (page) => {
  const routes = {
    'dashboard': '/pages/dashboard/dashboard',
    'charts': '/pages/charts/charts',
    'health': '/pages/health/health',
    'map': '/pages/map/map',
    'settings': '/pages/settings/settings',
    'notification': '/pages/notification/notification'
  }

  if (routes[page]) {
    uni.navigateTo({
      url: routes[page],
      fail: (err) => {
        console.error('导航失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  }
}

// 快速操作函数
const generateHealthReport = () => {
  uni.showLoading({ title: '生成中...' })
  
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '健康报告已生成',
      icon: 'success'
    })
    
    // 这里可以跳转到报告页面或下载报告
  }, 1500)
}

const setHealthReminder = () => {
  uni.showActionSheet({
    itemList: ['每日健康检查', '定期体检提醒', '辐射监测提醒', '运动提醒'],
    success: (res) => {
      const reminders = ['每日健康检查', '定期体检提醒', '辐射监测提醒', '运动提醒']
      uni.showToast({
        title: `已设置${reminders[res.tapIndex]}`,
        icon: 'success'
      })
    }
  })
}

const exportHealthData = () => {
  uni.showActionSheet({
    itemList: ['导出为PDF', '导出为Excel', '发送到邮箱'],
    success: (res) => {
      const options = ['PDF格式', 'Excel格式', '邮箱发送']
      uni.showLoading({ title: '导出中...' })
      
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: `${options[res.tapIndex]}导出成功`,
          icon: 'success'
        })
      }, 1500)
    }
  })
}

// 图表相关函数
const initHealthTrendChart = () => {
  isLoadingChart.value = true
  
  setTimeout(() => {
    const ctx = uni.createCanvasContext('healthTrendChart')
    if (!ctx) {
      isLoadingChart.value = false
      return
    }
    
    // 模拟绘制健康趋势图
    const width = chartWidth.value
    const height = 200
    const padding = 40
    
    // 清除画布
    ctx.clearRect(0, 0, width, height)
    
    // 背景
    ctx.setFillStyle('#ffffff')
    ctx.fillRect(0, 0, width, height)
    
    // 生成示例数据（7天的健康分数和暴露水平）
    const days = 7
    const healthData = Array.from({ length: days }, (_, i) => 
      85 + Math.sin(i * 0.5) * 10 + Math.random() * 5
    )
    const exposureData = Array.from({ length: days }, (_, i) => 
      15 + Math.cos(i * 0.7) * 5 + Math.random() * 3
    )
    
    const chartWidth = width - padding * 2
    const chartHeight = height - padding * 2
    const stepX = chartWidth / (days - 1)
    
    // 绘制健康分数线
    ctx.beginPath()
    ctx.setStrokeStyle('#10b981')
    ctx.setLineWidth(3)
    
    healthData.forEach((value, index) => {
      const x = padding + index * stepX
      const y = padding + chartHeight - (value - 60) / 40 * chartHeight
      
      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    ctx.stroke()
    
    // 绘制暴露水平线
    ctx.beginPath()
    ctx.setStrokeStyle('#ef4444')
    ctx.setLineWidth(2)
    
    exposureData.forEach((value, index) => {
      const x = padding + index * stepX
      const y = padding + chartHeight - value / 30 * chartHeight
      
      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    ctx.stroke()
    
    ctx.draw()
    isLoadingChart.value = false
  }, 1000)
}

// 生命周期
onMounted(() => {
  // 获取屏幕宽度
  const { windowWidth } = uni.getSystemInfoSync()
  chartWidth.value = windowWidth - 64 // 减去padding
  
  // 初始化图表
  setTimeout(() => {
    initHealthTrendChart()
  }, 500)
  
  // 模拟数据更新
  const updateInterval = setInterval(() => {
    // 模拟健康分数变化
    healthScore.value = Math.max(70, Math.min(100, 
      healthScore.value + (Math.random() - 0.5) * 2
    ))
    
    // 模拟暴露水平变化
    exposureLevel.value = Math.max(5, Math.min(30, 
      exposureLevel.value + (Math.random() - 0.5) * 1
    ))
  }, 5000)
  
  // 保存定时器引用用于清理
  onUnmounted(() => {
    clearInterval(updateInterval)
  })
})
</script>

<style scoped>
/* 全局容器样式 */
.health-container {
  min-height: 100vh;
  background: #ffffff;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
  padding-bottom: 120px;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

/* 状态栏 */
.status-bar {
  height: 44px;
  background: transparent;
}

/* 页面头部 */
.page-header {
  padding: 20px 16px 30px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 0 0 24px 24px;
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  flex: 1;
}

.title-text {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
}

.subtitle-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.health-status {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.health-status.excellent {
  background: rgba(16, 185, 129, 0.3);
}

.health-status.good {
  background: rgba(59, 130, 246, 0.3);
}

.health-status.warning {
  background: rgba(245, 158, 11, 0.3);
}

.health-status.danger {
  background: rgba(239, 68, 68, 0.3);
}

.status-icon {
  font-size: 20px;
}

/* 健康评分卡片 */
.health-score-card {
  margin: 0 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  gap: 20px;
}

.score-background {
  position: relative;
}

.score-circle {
  width: 120px;
  height: 120px;
  position: relative;
  background: conic-gradient(from 0deg, #e2e8f0, #e2e8f0);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: conic-gradient(from 0deg, #10b981 0deg, #10b981 calc(var(--score) * 1deg), #e2e8f0 calc(var(--score) * 1deg), #e2e8f0 360deg);
  border-radius: 50%;
  transition: transform 1s ease;
}

.score-content {
  position: relative;
  z-index: 2;
  text-align: center;
  background: #ffffff;
  border-radius: 50%;
  width: 90px;
  height: 90px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.score-number {
  font-size: 28px;
  font-weight: 700;
  color: #0f172a;
  line-height: 1;
}

.score-label {
  font-size: 12px;
  color: #64748b;
  margin-top: 2px;
}

.score-description {
  flex: 1;
}

.score-status {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 8px;
}

.score-advice {
  font-size: 14px;
  color: #64748b;
  line-height: 1.4;
}

/* 健康指标网格 */
.health-metrics {
  margin: 0 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.metrics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.metrics-title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
}

.metrics-time {
  font-size: 12px;
  color: #64748b;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metric-card {
  background: #f8fafc;
  border-radius: 16px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.metric-card.exposure {
  border-left: 4px solid #ef4444;
}

.metric-card.dose {
  border-left: 4px solid #f59e0b;
}

.metric-card.duration {
  border-left: 4px solid #3b82f6;
}

.metric-card.risk {
  border-left: 4px solid #8b5cf6;
}

.metric-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(59, 130, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.metric-icon svg {
  width: 16px;
  height: 16px;
  color: #3b82f6;
}

.metric-content {
  margin-bottom: 12px;
}

.metric-label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
}

.metric-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 2px;
}

.metric-unit {
  font-size: 12px;
  color: #94a3b8;
}

.metric-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 14px;
  background: rgba(248, 250, 252, 0.8);
  position: absolute;
  top: 16px;
  right: 16px;
}

.trend-icon {
  font-size: 14px;
}

.metric-trend.up { background: rgba(239, 68, 68, 0.1); }
.metric-trend.down { background: rgba(16, 185, 129, 0.1); }
.metric-trend.stable { background: rgba(156, 163, 175, 0.1); }

/* 健康建议卡片 */
.health-advice-section {
  margin: 0 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.advice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.advice-title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
}

.ai-badge {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: #ffffff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.advice-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.advice-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border-left: 4px solid #e2e8f0;
}

.advice-item.important {
  border-left-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.advice-item.suggestion {
  border-left-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.advice-item.info {
  border-left-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.advice-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.advice-content {
  flex: 1;
}

.advice-text {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 4px;
}

.advice-detail {
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
}

.advice-priority {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 600;
  flex-shrink: 0;
}

.advice-priority.high {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.advice-priority.medium {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.advice-priority.low {
  background: rgba(156, 163, 175, 0.1);
  color: #6b7280;
}

/* 历史趋势图表 */
.trend-chart-section {
  margin: 0 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
}

.chart-period {
  background: #f8fafc;
  padding: 6px 12px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.period-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
}

.chart-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background: #f8fafc;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 16px;
}

.trend-chart {
  border-radius: 12px;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-loading text {
  font-size: 14px;
  color: #64748b;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 6px;
}

.legend-color.health {
  background: linear-gradient(135deg, #10b981, #059669);
}

.legend-color.exposure {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.legend-item text {
  font-size: 12px;
  color: #64748b;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 12px;
  margin: 0 16px 20px;
}

.action-button {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-button:active {
  transform: translateY(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-button.report {
  border-top: 4px solid #3b82f6;
}

.action-button.reminder {
  border-top: 4px solid #10b981;
}

.action-button.export {
  border-top: 4px solid #f59e0b;
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(248, 250, 252, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.action-text {
  font-size: 12px;
  color: #0f172a;
  font-weight: 600;
  text-align: center;
}

/* 全局统一底部导航栏样式 */
.modern-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 0 20px 20px;
}

.nav-background {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 30%,
    rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.nav-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24px;
  padding: 12px 16px;
  margin: 0 8px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.nav-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  border-radius: 16px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  min-width: 60px;
}

.nav-tab.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow:
    0 8px 24px rgba(59, 130, 246, 0.3),
    0 4px 12px rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.nav-tab:hover:not(.active) {
  background: rgba(59, 130, 246, 0.08);
  transform: translateY(-1px);
}

.tab-icon-container {
  position: relative;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.tab-icon {
  width: 22px;
  height: 22px;
  color: #64748b;
  transition: all 0.3s ease;
}

.nav-tab.active .tab-icon {
  color: #ffffff;
  transform: scale(1.1);
}

.tab-label {
  font-size: 11px;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
}

.nav-tab.active .tab-label {
  color: #ffffff;
  font-weight: 600;
}

.notification-dot {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

/* 响应式优化 */
@media (max-width: 375px) {
  .health-score-card {
    flex-direction: column;
    text-align: center;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    flex-direction: column;
  }
}

/* 页面动画 */
.health-container > * {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header { animation-delay: 0.1s; }
.health-score-card { animation-delay: 0.2s; }
.health-metrics { animation-delay: 0.3s; }
.health-advice-section { animation-delay: 0.4s; }
.trend-chart-section { animation-delay: 0.5s; }
.quick-actions { animation-delay: 0.6s; }
</style> 