<template>
  <view class="advanced-chart-container">
    <!-- 重新设计的图表头部 -->
    <view class="chart-header-modern">
      <view class="header-main">
        <view class="chart-icon-wrapper">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
          </svg>
        </view>
        <view class="chart-title-section">
          <text class="chart-title-modern">{{ title }}</text>
          <text class="chart-subtitle-modern">{{ subtitle }}</text>
        </view>
      </view>

      <view class="header-controls">
        <!-- 时间段选择器 -->
        <view class="time-selector-modern">
          <view
            v-for="period in timePeriods"
            :key="period.value"
            class="time-option-modern"
            :class="{ active: selectedPeriod === period.value }"
            @click="selectPeriod(period.value)"
          >
            {{ period.label }}
          </view>
        </view>

        <!-- 图表类型切换 -->
        <view class="chart-type-toggle-modern">
          <view
            class="toggle-option-modern"
            :class="{ active: chartType === 'line' }"
            @click="setChartType('line')"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="22,6 13.5,15.5 8.5,10.5 2,17"></polyline>
              <polyline points="16,6 22,6 22,12"></polyline>
            </svg>
          </view>
          <view
            class="toggle-option-modern"
            :class="{ active: chartType === 'bar' }"
            @click="setChartType('bar')"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="12" y1="20" x2="12" y2="10"></line>
              <line x1="18" y1="20" x2="18" y2="4"></line>
              <line x1="6" y1="20" x2="6" y2="16"></line>
            </svg>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 图表容器 -->
    <view class="chart-canvas-container">
      <canvas 
        :canvas-id="canvasId" 
        class="chart-canvas"
        :style="{ width: chartWidth + 'px', height: chartHeight + 'px' }"
      ></canvas>
      
      <!-- 误差带指示器 -->
      <view class="error-band-indicator" v-if="showErrorBand">
        <view class="indicator-item">
          <view class="indicator-color" style="background: rgba(59, 130, 246, 0.2);"></view>
          <text class="indicator-text">±5% 误差带</text>
        </view>
      </view>
    </view>
    
    <!-- 数据统计 -->
    <view class="chart-statistics">
      <view class="stat-item">
        <text class="stat-label">平均值</text>
        <text class="stat-value">{{ statistics.average }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">最大值</text>
        <text class="stat-value">{{ statistics.max }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">最小值</text>
        <text class="stat-value">{{ statistics.min }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">数据点</text>
        <text class="stat-value">{{ statistics.count }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'

export default {
  name: 'AdvancedChart',
  props: {
    title: {
      type: String,
      default: '数据趋势'
    },
    subtitle: {
      type: String,
      default: '实时监测数据'
    },
    data: {
      type: Array,
      default: () => []
    },
    canvasId: {
      type: String,
      required: true
    },
    chartWidth: {
      type: Number,
      default: 350
    },
    chartHeight: {
      type: Number,
      default: 200
    },
    showErrorBand: {
      type: Boolean,
      default: true
    }
  },
  
  setup(props) {
    const chartContext = ref(null)
    const selectedPeriod = ref('1h')
    const chartType = ref('line')
    
    const timePeriods = ref([
      { label: '1小时', value: '1h' },
      { label: '6小时', value: '6h' },
      { label: '24小时', value: '24h' },
      { label: '7天', value: '7d' }
    ])
    
    // 计算统计数据
    const statistics = computed(() => {
      if (!props.data || props.data.length === 0) {
        return { average: '0.00', max: '0.00', min: '0.00', count: 0 }
      }
      
      const values = props.data.map(item => item.value || item.doseRate || 0)
      const sum = values.reduce((a, b) => a + b, 0)
      const avg = sum / values.length
      const max = Math.max(...values)
      const min = Math.min(...values)
      
      return {
        average: avg.toFixed(2),
        max: max.toFixed(2),
        min: min.toFixed(2),
        count: values.length
      }
    })
    
    // 选择时间段
    const selectPeriod = (period) => {
      selectedPeriod.value = period
      drawChart()
    }
    
    // 设置图表类型
    const setChartType = (type) => {
      chartType.value = type
      drawChart()
    }
    
    // 初始化图表
    const initChart = () => {
      const ctx = uni.createCanvasContext(props.canvasId)
      if (!ctx) return
      
      chartContext.value = ctx
      drawChart()
    }
    
    // 绘制图表
    const drawChart = () => {
      if (!chartContext.value) return
      
      const ctx = chartContext.value
      const width = props.chartWidth
      const height = props.chartHeight
      const padding = 40
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      
      // 清空画布
      ctx.clearRect(0, 0, width, height)
      
      // 获取数据
      const data = getFilteredData()
      if (data.length === 0) return
      
      const values = data.map(item => item.value || item.doseRate || 0)
      const maxValue = Math.max(...values)
      const minValue = Math.min(...values)
      const range = maxValue - minValue || 1
      
      // 绘制背景网格
      drawGrid(ctx, width, height, padding)
      
      // 绘制误差带
      if (props.showErrorBand) {
        drawErrorBand(ctx, data, width, height, padding, minValue, range)
      }
      
      // 绘制图表
      if (chartType.value === 'line') {
        drawLineChart(ctx, data, width, height, padding, minValue, range)
      } else {
        drawBarChart(ctx, data, width, height, padding, minValue, range)
      }
      
      // 绘制坐标轴
      drawAxes(ctx, width, height, padding)
      
      ctx.draw()
    }
    
    // 获取过滤后的数据
    const getFilteredData = () => {
      if (!props.data || props.data.length === 0) return []
      
      const now = new Date()
      let startTime
      
      switch (selectedPeriod.value) {
        case '1h':
          startTime = new Date(now.getTime() - 60 * 60 * 1000)
          break
        case '6h':
          startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000)
          break
        case '24h':
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          break
        case '7d':
          startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        default:
          return props.data.slice(0, 50)
      }
      
      return props.data.filter(item => {
        const itemTime = new Date(item.timestamp || item.time || now)
        return itemTime >= startTime
      }).slice(0, 100) // 限制最大数据点数
    }
    
    // 绘制网格
    const drawGrid = (ctx, width, height, padding) => {
      ctx.setStrokeStyle('rgba(0, 0, 0, 0.05)')
      ctx.setLineWidth(0.5)
      
      // 水平网格线
      for (let i = 1; i < 5; i++) {
        const y = padding + (i * (height - padding * 2) / 5)
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }
      
      // 垂直网格线
      for (let i = 1; i < 6; i++) {
        const x = padding + (i * (width - padding * 2) / 6)
        ctx.beginPath()
        ctx.moveTo(x, padding)
        ctx.lineTo(x, height - padding)
        ctx.stroke()
      }
    }
    
    // 绘制误差带
    const drawErrorBand = (ctx, data, width, height, padding, minValue, range) => {
      if (data.length < 2) return
      
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      
      // 计算平均值
      const values = data.map(item => item.value || item.doseRate || 0)
      const average = values.reduce((a, b) => a + b, 0) / values.length
      
      // 5% 误差带
      const errorPercent = 0.05
      const upperBound = average * (1 + errorPercent)
      const lowerBound = average * (1 - errorPercent)
      
      // 绘制误差带
      ctx.beginPath()
      ctx.moveTo(padding, height - padding - ((lowerBound - minValue) / range) * chartHeight)
      
      for (let i = 0; i < data.length; i++) {
        const x = padding + (i / (data.length - 1)) * chartWidth
        const upperY = height - padding - ((upperBound - minValue) / range) * chartHeight
        const lowerY = height - padding - ((lowerBound - minValue) / range) * chartHeight
        
        if (i === 0) {
          ctx.lineTo(x, upperY)
        } else {
          ctx.lineTo(x, upperY)
        }
      }
      
      for (let i = data.length - 1; i >= 0; i--) {
        const x = padding + (i / (data.length - 1)) * chartWidth
        const lowerY = height - padding - ((lowerBound - minValue) / range) * chartHeight
        ctx.lineTo(x, lowerY)
      }
      
      ctx.closePath()
      ctx.setFillStyle('rgba(59, 130, 246, 0.1)')
      ctx.fill()
    }
    
    // 绘制折线图
    const drawLineChart = (ctx, data, width, height, padding, minValue, range) => {
      if (data.length < 2) return
      
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      
      // 绘制渐变填充
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)
      
      data.forEach((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth
        const value = item.value || item.doseRate || 0
        const y = height - padding - ((value - minValue) / range) * chartHeight
        
        if (index === 0) {
          ctx.lineTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      
      ctx.lineTo(width - padding, height - padding)
      
      const gradient = ctx.createLinearGradient(0, padding, 0, height - padding)
      gradient.addColorStop(0, 'rgba(59, 130, 246, 0.3)')
      gradient.addColorStop(1, 'rgba(59, 130, 246, 0.05)')
      ctx.setFillStyle(gradient)
      ctx.fill()
      
      // 绘制线条
      ctx.beginPath()
      ctx.setStrokeStyle('#3b82f6')
      ctx.setLineWidth(3)
      ctx.setLineCap('round')
      ctx.setLineJoin('round')
      
      data.forEach((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth
        const value = item.value || item.doseRate || 0
        const y = height - padding - ((value - minValue) / range) * chartHeight
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      ctx.stroke()
      
      // 绘制数据点
      data.forEach((item, index) => {
        if (index % Math.ceil(data.length / 10) === 0) { // 只显示部分点
          const x = padding + (index / (data.length - 1)) * chartWidth
          const value = item.value || item.doseRate || 0
          const y = height - padding - ((value - minValue) / range) * chartHeight
          
          ctx.beginPath()
          ctx.arc(x, y, 4, 0, 2 * Math.PI)
          ctx.setFillStyle('#ffffff')
          ctx.fill()
          ctx.beginPath()
          ctx.arc(x, y, 3, 0, 2 * Math.PI)
          ctx.setFillStyle('#3b82f6')
          ctx.fill()
        }
      })
    }
    
    // 绘制柱状图
    const drawBarChart = (ctx, data, width, height, padding, minValue, range) => {
      if (data.length === 0) return
      
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      const barWidth = chartWidth / data.length * 0.8
      const barSpacing = chartWidth / data.length * 0.2
      
      data.forEach((item, index) => {
        const x = padding + (index * chartWidth / data.length) + barSpacing / 2
        const value = item.value || item.doseRate || 0
        const barHeight = ((value - minValue) / range) * chartHeight
        const y = height - padding - barHeight
        
        // 创建渐变
        const gradient = ctx.createLinearGradient(0, y, 0, height - padding)
        gradient.addColorStop(0, '#3b82f6')
        gradient.addColorStop(1, '#1d4ed8')
        
        ctx.beginPath()
        ctx.rect(x, y, barWidth, barHeight)
        ctx.setFillStyle(gradient)
        ctx.fill()
      })
    }
    
    // 绘制坐标轴
    const drawAxes = (ctx, width, height, padding) => {
      ctx.setStrokeStyle('#e5e7eb')
      ctx.setLineWidth(1)
      
      // X轴
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)
      ctx.lineTo(width - padding, height - padding)
      ctx.stroke()
      
      // Y轴
      ctx.beginPath()
      ctx.moveTo(padding, padding)
      ctx.lineTo(padding, height - padding)
      ctx.stroke()
    }
    
    onMounted(() => {
      setTimeout(() => {
        initChart()
      }, 100)
    })
    
    watch(() => props.data, () => {
      drawChart()
    }, { deep: true })
    
    return {
      selectedPeriod,
      chartType,
      timePeriods,
      statistics,
      selectPeriod,
      setChartType
    }
  }
}
</script>

<style scoped>
.advanced-chart-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 20px;
  padding: 16px;
  margin: 12px 16px; /* 与首页卡片左右间距一致 */
  box-shadow:
    0 6px 24px rgba(0, 0, 0, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.advanced-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 120px;
  height: 120px;
  background: radial-gradient(circle at center, rgba(59, 130, 246, 0.08) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(60%, -60%);
}

.chart-header-modern {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.header-main {
  display: flex;
  align-items: center;
  gap: 16px;
}

.chart-icon-wrapper {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 18px rgba(59, 130, 246, 0.25);
}

.chart-icon-wrapper svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
}

.chart-title-modern {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  display: block;
  line-height: 1.2;
}

.chart-subtitle-modern {
  font-size: 13px;
  color: #64748b;
  display: block;
  margin-top: 2px;
  font-weight: 500;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.time-selector {
  display: flex;
  gap: 8px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  padding: 4px;
}

.time-option {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-option.active {
  background: #3b82f6;
  color: #ffffff;
  font-weight: 500;
}

.chart-type-toggle {
  display: flex;
  gap: 8px;
}

.toggle-option {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(248, 250, 252, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-option.active {
  background: #3b82f6;
}

.toggle-option svg {
  width: 20px;
  height: 20px;
  color: #64748b;
}

.toggle-option.active svg {
  color: #ffffff;
}

/* 现代化时间选择器 */
.time-selector-modern {
  display: flex;
  gap: 6px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  padding: 6px;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.time-option-modern {
  padding: 8px 14px;
  border-radius: 12px;
  font-size: 12px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  min-width: 32px;
  text-align: center;
}

.time-option-modern:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.time-option-modern.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

/* 现代化图表类型切换 */
.chart-type-toggle-modern {
  display: flex;
  gap: 6px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  padding: 6px;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.toggle-option-modern {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-option-modern:hover {
  background: rgba(59, 130, 246, 0.1);
}

.toggle-option-modern svg {
  width: 18px;
  height: 18px;
  color: #64748b;
  transition: all 0.3s ease;
}

.toggle-option-modern.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.toggle-option-modern.active svg {
  color: #ffffff;
}

.chart-canvas-container {
  position: relative;
  margin-bottom: 20px;
}

.chart-canvas {
  width: 100%;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.5);
}

.error-band-indicator {
  display: flex;
  justify-content: center;
  margin-top: 12px;
}

.indicator-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.indicator-color {
  width: 16px;
  height: 4px;
  border-radius: 2px;
}

.indicator-text {
  font-size: 12px;
  color: #64748b;
}

.chart-statistics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-top: 20px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 12px;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}
</style>
