<template>
  <view class="settings-container">
    <!-- 状态栏 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <view class="page-title">
          <text class="title-text">设置</text>
          <text class="subtitle-text">个性化配置 • 系统管理</text>
        </view>
        <view class="profile-avatar" @tap="editProfile">
          <text class="avatar-text">{{ userProfile.name.charAt(0) }}</text>
        </view>
      </view>
    </view>

    <!-- 用户信息卡片 -->
    <view class="user-profile-card">
      <view class="profile-info">
        <view class="user-avatar">
          <text class="avatar-initial">{{ userProfile.name.charAt(0) }}</text>
        </view>
        <view class="user-details">
          <text class="user-name">{{ userProfile.name }}</text>
          <text class="user-subtitle">{{ userProfile.email }}</text>
          <view class="user-stats">
            <view class="stat-item">
              <text class="stat-value">{{ userProfile.monitoringDays }}</text>
              <text class="stat-label">监测天数</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{ userProfile.healthScore }}</text>
              <text class="stat-label">健康分数</text>
            </view>
          </view>
        </view>
      </view>
      <view class="profile-actions">
        <view class="action-btn edit" @tap="editProfile">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
        </view>
      </view>
    </view>

    <!-- 设置分组 -->
    <view class="settings-sections">
      <!-- 监测设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">监测设置</text>
          <text class="section-subtitle">配置辐射监测参数</text>
        </view>
        <view class="settings-items">
          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon dose-rate">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">剂量率报警阈值</text>
                <text class="setting-desc">设置触发报警的剂量率水平</text>
              </view>
            </view>
            <view class="setting-control">
              <text class="control-value">{{ settings.maxDoseRate.toFixed(2) }} μSv/h</text>
              <view class="control-arrow" @tap="adjustDoseRateThreshold">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon interval">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 6v6l4 2"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">监测间隔</text>
                <text class="setting-desc">数据采集的时间间隔</text>
              </view>
            </view>
            <view class="setting-control">
              <text class="control-value">{{ settings.monitoringInterval }}秒</text>
              <view class="control-arrow" @tap="adjustMonitoringInterval">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon upload">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <path d="M7 10l5-5 5 5"></path>
                  <path d="M12 15V5"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">自动上传数据</text>
                <text class="setting-desc">自动备份监测数据到云端</text>
              </view>
            </view>
            <view class="setting-control">
              <switch :checked="settings.autoUpload" @change="toggleAutoUpload" color="#3b82f6" />
            </view>
          </view>
        </view>
      </view>

      <!-- 通知设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">通知设置</text>
          <text class="section-subtitle">管理提醒和通知方式</text>
        </view>
        <view class="settings-items">
          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon sound">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M11 5L6 9H2v6h4l5 4V5z"></path>
                  <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">声音提醒</text>
                <text class="setting-desc">超出阈值时播放提示音</text>
              </view>
            </view>
            <view class="setting-control">
              <switch :checked="settings.soundAlert" @change="toggleSoundAlert" color="#3b82f6" />
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon vibration">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M7 3h10v18H7z"></path>
                  <path d="M3 8h2"></path>
                  <path d="M3 12h2"></path>
                  <path d="M3 16h2"></path>
                  <path d="M19 8h2"></path>
                  <path d="M19 12h2"></path>
                  <path d="M19 16h2"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">震动提醒</text>
                <text class="setting-desc">设备震动提醒功能</text>
              </view>
            </view>
            <view class="setting-control">
              <switch :checked="settings.vibrationAlert" @change="toggleVibrationAlert" color="#3b82f6" />
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon push">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                  <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">推送通知</text>
                <text class="setting-desc">接收重要事件推送消息</text>
              </view>
            </view>
            <view class="setting-control">
              <switch :checked="settings.pushNotification" @change="togglePushNotification" color="#3b82f6" />
            </view>
          </view>
        </view>
      </view>

      <!-- 显示设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">显示设置</text>
          <text class="section-subtitle">界面和显示偏好设置</text>
        </view>
        <view class="settings-items">
          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon theme">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="5"></circle>
                  <path d="M12 1v2"></path>
                  <path d="M12 21v2"></path>
                  <path d="M4.22 4.22l1.42 1.42"></path>
                  <path d="M18.36 18.36l1.42 1.42"></path>
                  <path d="M1 12h2"></path>
                  <path d="M21 12h2"></path>
                  <path d="M4.22 19.78l1.42-1.42"></path>
                  <path d="M18.36 5.64l1.42-1.42"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">主题模式</text>
                <text class="setting-desc">选择浅色或深色主题</text>
              </view>
            </view>
            <view class="setting-control">
              <text class="control-value">{{ settings.theme === 'light' ? '浅色' : '深色' }}</text>
              <view class="control-arrow" @tap="toggleTheme">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon language">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M5 8l6 6"></path>
                  <path d="M4 14l6-6 2-3"></path>
                  <path d="M2 5h12"></path>
                  <path d="M7 2h1"></path>
                  <path d="M22 22l-5-10-5 10"></path>
                  <path d="M14 18h6"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">语言</text>
                <text class="setting-desc">选择应用程序语言</text>
              </view>
            </view>
            <view class="setting-control">
              <text class="control-value">简体中文</text>
              <view class="control-arrow" @tap="changeLanguage">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon units">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <path d="M14 2v6h6"></path>
                  <path d="M16 13H8"></path>
                  <path d="M16 17H8"></path>
                  <path d="M10 9H8"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">测量单位</text>
                <text class="setting-desc">选择数据显示单位</text>
              </view>
            </view>
            <view class="setting-control">
              <text class="control-value">{{ settings.units }}</text>
              <view class="control-arrow" @tap="changeUnits">
                <text>></text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 数据管理 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">数据管理</text>
          <text class="section-subtitle">备份、导出和清理数据</text>
        </view>
        <view class="settings-items">
          <view class="setting-item" @tap="exportData">
            <view class="setting-info">
              <view class="setting-icon export">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <path d="M14 2v6h6"></path>
                  <path d="M16 13H8"></path>
                  <path d="M16 17H8"></path>
                  <path d="M10 9H8"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">导出数据</text>
                <text class="setting-desc">导出所有监测数据</text>
              </view>
            </view>
            <view class="setting-control">
              <view class="control-arrow">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item" @tap="backupData">
            <view class="setting-info">
              <view class="setting-icon backup">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <path d="M22 4L12 14.01l-3-3"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">数据备份</text>
                <text class="setting-desc">备份数据到云端</text>
              </view>
            </view>
            <view class="setting-control">
              <view class="control-arrow">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item" @tap="clearData">
            <view class="setting-info">
              <view class="setting-icon clear">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 6h18"></path>
                  <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                  <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                  <path d="M10 11v6"></path>
                  <path d="M14 11v6"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">清理数据</text>
                <text class="setting-desc">删除历史监测数据</text>
              </view>
            </view>
            <view class="setting-control">
              <view class="control-arrow">
                <text>></text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 关于 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">关于</text>
          <text class="section-subtitle">应用信息和帮助</text>
        </view>
        <view class="settings-items">
          <view class="setting-item" @tap="showAbout">
            <view class="setting-info">
              <view class="setting-icon info">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 16v-4"></path>
                  <path d="M12 8h.01"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">应用信息</text>
                <text class="setting-desc">版本号、开发者信息</text>
              </view>
            </view>
            <view class="setting-control">
              <text class="control-value">v1.0.0</text>
              <view class="control-arrow">
                <text>></text>
              </view>
            </view>
          </view>

          <view class="setting-item" @tap="showHelp">
            <view class="setting-info">
              <view class="setting-icon help">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                  <path d="M12 17h.01"></path>
                </svg>
              </view>
              <view class="setting-details">
                <text class="setting-title">帮助与支持</text>
                <text class="setting-desc">使用指南和技术支持</text>
              </view>
            </view>
            <view class="setting-control">
              <view class="control-arrow">
                <text>></text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 全局统一的底部导航栏 -->
    <view class="modern-bottom-nav">
      <view class="nav-background"></view>
      <view class="nav-content">
        <view class="nav-tab" @tap="navigateTo('dashboard')">
          <view class="tab-icon-container">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="9" y1="9" x2="9" y2="15"></line>
              <line x1="15" y1="9" x2="15" y2="15"></line>
            </svg>
          </view>
          <text class="tab-label">仪表盘</text>
        </view>

        <view class="nav-tab" @tap="navigateTo('charts')">
          <view class="tab-icon-container">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
            </svg>
          </view>
          <text class="tab-label">数据</text>
        </view>

        <view class="nav-tab" @tap="navigateTo('health')">
          <view class="tab-icon-container">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"></path>
            </svg>
          </view>
          <text class="tab-label">健康</text>
        </view>

        <view class="nav-tab" @tap="navigateTo('map')">
          <view class="tab-icon-container">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </view>
          <text class="tab-label">地图</text>
        </view>

        <view class="nav-tab active" @tap="navigateTo('settings')">
          <view class="tab-icon-container">
            <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-6.5l-4.24 4.24M7.76 7.76L3.52 3.52m12.96 12.96l-4.24-4.24M7.76 16.24l-4.24 4.24"></path>
            </svg>
          </view>
          <text class="tab-label">设置</text>
        </view>
      </view>
    </view>
    <!-- 底部导航栏 -->
    <BottomNavigation currentPage="settings" />
  </view>
</template>

<script>
import { ref, computed } from 'vue'
import { radiationState } from '../../utils/dataStore.js'
import BottomNavigation from '../../components/BottomNavigation.vue'

// 用户配置数据
const userProfile = ref({
  name: '张三',
  email: '<EMAIL>',
  monitoringDays: 127,
  healthScore: 85
})

// 设置数据
const settings = ref({
  maxDoseRate: 1.0,
  monitoringInterval: 10,
  autoUpload: true,
  soundAlert: true,
  vibrationAlert: true,
  pushNotification: true,
  theme: 'light',
  units: 'μSv/h'
})

// 统一的导航函数
const navigateTo = (page) => {
  const routes = {
    'dashboard': '/pages/dashboard/dashboard',
    'charts': '/pages/charts/charts',
    'health': '/pages/health/health',
    'map': '/pages/map/map',
    'settings': '/pages/settings/settings',
    'notification': '/pages/notification/notification'
  }

  if (routes[page]) {
    uni.navigateTo({
      url: routes[page],
      fail: (err) => {
        console.error('导航失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  }
}

// 编辑用户资料
const editProfile = () => {
  uni.showModal({
    title: '编辑资料',
    content: '功能开发中...',
    showCancel: false,
    confirmText: '确定'
  })
}

// 调整剂量率阈值
const adjustDoseRateThreshold = () => {
  uni.showActionSheet({
    itemList: ['0.5 μSv/h', '1.0 μSv/h', '2.0 μSv/h', '5.0 μSv/h', '自定义'],
    success: (res) => {
      const values = [0.5, 1.0, 2.0, 5.0]
      if (res.tapIndex < 4) {
        settings.value.maxDoseRate = values[res.tapIndex]
        uni.showToast({
          title: `已设置为 ${values[res.tapIndex]} μSv/h`,
          icon: 'success'
        })
      } else {
        // 自定义输入
        uni.showModal({
          title: '自定义阈值',
          content: '请输入剂量率阈值（μSv/h）',
          editable: true,
          placeholderText: '1.0',
          success: (res) => {
            if (res.confirm && res.content) {
              const value = parseFloat(res.content)
              if (!isNaN(value) && value > 0) {
                settings.value.maxDoseRate = value
                uni.showToast({
                  title: `已设置为 ${value} μSv/h`,
                  icon: 'success'
                })
              } else {
                uni.showToast({
                  title: '请输入有效数值',
                  icon: 'none'
                })
              }
            }
          }
        })
      }
    }
  })
}

// 调整监测间隔
const adjustMonitoringInterval = () => {
  uni.showActionSheet({
    itemList: ['1秒', '5秒', '10秒', '30秒', '60秒'],
    success: (res) => {
      const values = [1, 5, 10, 30, 60]
      settings.value.monitoringInterval = values[res.tapIndex]
      uni.showToast({
        title: `已设置为 ${values[res.tapIndex]}秒`,
        icon: 'success'
      })
    }
  })
}

// 切换开关函数
const toggleAutoUpload = (e) => {
  settings.value.autoUpload = e.detail.value
  uni.showToast({
    title: settings.value.autoUpload ? '已开启自动上传' : '已关闭自动上传',
    icon: 'success'
  })
}

const toggleSoundAlert = (e) => {
  settings.value.soundAlert = e.detail.value
  uni.showToast({
    title: settings.value.soundAlert ? '已开启声音提醒' : '已关闭声音提醒',
    icon: 'success'
  })
}

const toggleVibrationAlert = (e) => {
  settings.value.vibrationAlert = e.detail.value
  uni.showToast({
    title: settings.value.vibrationAlert ? '已开启震动提醒' : '已关闭震动提醒',
    icon: 'success'
  })
}

const togglePushNotification = (e) => {
  settings.value.pushNotification = e.detail.value
  uni.showToast({
    title: settings.value.pushNotification ? '已开启推送通知' : '已关闭推送通知',
    icon: 'success'
  })
}

// 主题切换
const toggleTheme = () => {
  settings.value.theme = settings.value.theme === 'light' ? 'dark' : 'light'
  uni.showToast({
    title: `已切换为${settings.value.theme === 'light' ? '浅色' : '深色'}主题`,
    icon: 'success'
  })
}

// 语言设置
const changeLanguage = () => {
  uni.showActionSheet({
    itemList: ['简体中文', 'English', '繁體中文', '日本語'],
    success: (res) => {
      const languages = ['简体中文', 'English', '繁體中文', '日本語']
      uni.showToast({
        title: `语言已切换为 ${languages[res.tapIndex]}`,
        icon: 'success'
      })
    }
  })
}

// 单位设置
const changeUnits = () => {
  uni.showActionSheet({
    itemList: ['μSv/h', 'mSv/h', 'R/h', 'mR/h'],
    success: (res) => {
      const units = ['μSv/h', 'mSv/h', 'R/h', 'mR/h']
      settings.value.units = units[res.tapIndex]
      uni.showToast({
        title: `单位已设置为 ${units[res.tapIndex]}`,
        icon: 'success'
      })
    }
  })
}

// 数据管理函数
const exportData = () => {
  uni.showLoading({ title: '导出中...' })
  
  setTimeout(() => {
    uni.hideLoading()
    uni.showActionSheet({
      itemList: ['导出为Excel', '导出为CSV', '导出为JSON', '发送到邮箱'],
      success: (res) => {
        const options = ['Excel格式', 'CSV格式', 'JSON格式', '邮箱发送']
        uni.showToast({
          title: `${options[res.tapIndex]}导出成功`,
          icon: 'success'
        })
      }
    })
  }, 1500)
}

const backupData = () => {
  uni.showLoading({ title: '备份中...' })
  
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '数据备份成功',
      icon: 'success'
    })
  }, 2000)
}

const clearData = () => {
  uni.showModal({
    title: '确认清理',
    content: '此操作将删除所有历史监测数据，且无法恢复。确定要继续吗？',
    confirmText: '确定',
    confirmColor: '#ef4444',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({ title: '清理中...' })
        
        setTimeout(() => {
          uni.hideLoading()
          uni.showToast({
            title: '数据清理完成',
            icon: 'success'
          })
        }, 1500)
      }
    }
  })
}

// 关于和帮助函数
const showAbout = () => {
  uni.showModal({
    title: '关于应用',
    content: '智能辐射监测系统 v1.0.0\n\n开发者：科技有限公司\n发布日期：2024年\n\n本应用致力于提供专业的辐射监测服务。',
    showCancel: false,
    confirmText: '确定'
  })
}

const showHelp = () => {
  uni.showActionSheet({
    itemList: ['使用指南', '常见问题', '视频教程', '联系客服'],
    success: (res) => {
      const options = ['使用指南', '常见问题', '视频教程', '联系客服']
      uni.showToast({
        title: `正在打开${options[res.tapIndex]}`,
        icon: 'success'
      })
    }
  })
}

export default {
  name: 'Settings',
  components: {
    BottomNavigation
  },
  setup() {
    return {
      userProfile,
      settings,
      navigateTo,
      adjustDoseRateThreshold,
      adjustMonitoringInterval,
      toggleAutoUpload,
      toggleSoundAlert,
      toggleVibrationAlert,
      togglePushNotification,
      toggleTheme,
      changeLanguage,
      changeUnits,
      exportData,
      backupData,
      clearData,
      showAbout,
      showHelp
    }
  }
}
</script>

<style scoped>
/* 全局容器样式 */
.settings-container {
  min-height: 100vh;
  background: #ffffff;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
  padding-bottom: 120px;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

/* 状态栏 */
.status-bar {
  height: 44px;
  background: transparent;
}

/* 页面头部 */
.page-header {
  padding: 20px 16px 30px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 0 0 24px 24px;
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  flex: 1;
}

.title-text {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
}

.subtitle-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.profile-avatar {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 16px;
  font-weight: 700;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
  transition: all 0.3s ease;
}

.profile-avatar:active {
  transform: scale(0.95);
}

.avatar-text {
  font-size: 16px;
}

/* 用户信息卡片 */
.user-profile-card {
  margin: 0 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 20px;
  font-weight: 700;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.avatar-initial {
  font-size: 20px;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 4px;
}

.user-subtitle {
  display: block;
  font-size: 14px;
  color: #64748b;
  margin-bottom: 12px;
}

.user-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
}

.profile-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(248, 250, 252, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.action-btn.edit {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: rgba(16, 185, 129, 0.3);
}

.action-btn svg {
  width: 16px;
  height: 16px;
  color: #ffffff;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 设置分组 */
.settings-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 0 16px;
}

.settings-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 4px;
}

.section-subtitle {
  font-size: 14px;
  color: #64748b;
}

.settings-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.setting-item:active {
  background: #f1f5f9;
  transform: scale(0.98);
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.setting-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.setting-icon.dose-rate {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.setting-icon.interval {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.setting-icon.upload {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.setting-icon.sound {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.setting-icon.vibration {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.setting-icon.push {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.setting-icon.theme {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
}

.setting-icon.language {
  background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
}

.setting-icon.units {
  background: linear-gradient(135deg, #84cc16 0%, #65a30d 100%);
}

.setting-icon.export {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

.setting-icon.backup {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.setting-icon.clear {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.setting-icon.info {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
}

.setting-icon.help {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
}

.setting-icon svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
}

.setting-details {
  flex: 1;
}

.setting-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 4px;
}

.setting-desc {
  font-size: 14px;
  color: #64748b;
  line-height: 1.4;
}

.setting-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-value {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.control-arrow {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(59, 130, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.control-arrow text {
  font-size: 14px;
  color: #3b82f6;
  font-weight: 600;
}

.control-arrow:active {
  background: rgba(59, 130, 246, 0.2);
  transform: scale(0.9);
}

/* 全局统一底部导航栏样式 */
.modern-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 0 20px 20px;
}

.nav-background {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 30%,
    rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.nav-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24px;
  padding: 12px 16px;
  margin: 0 8px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.nav-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  border-radius: 16px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  min-width: 60px;
}

.nav-tab.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow:
    0 8px 24px rgba(59, 130, 246, 0.3),
    0 4px 12px rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.nav-tab:hover:not(.active) {
  background: rgba(59, 130, 246, 0.08);
  transform: translateY(-1px);
}

.tab-icon-container {
  position: relative;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.tab-icon {
  width: 22px;
  height: 22px;
  color: #64748b;
  transition: all 0.3s ease;
}

.nav-tab.active .tab-icon {
  color: #ffffff;
  transform: scale(1.1);
}

.tab-label {
  font-size: 11px;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
}

.nav-tab.active .tab-label {
  color: #ffffff;
  font-weight: 600;
}

/* 响应式优化 */
@media (max-width: 375px) {
  .user-profile-card {
    flex-direction: column;
    text-align: center;
  }
  
  .user-stats {
    justify-content: center;
  }
  
  .settings-sections {
    margin: 0 12px;
  }
}

/* 页面动画 */
.settings-container > * {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header { animation-delay: 0.1s; }
.user-profile-card { animation-delay: 0.2s; }
.settings-sections { animation-delay: 0.3s; }
</style>