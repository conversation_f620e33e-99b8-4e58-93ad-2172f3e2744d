<template>
  <view class="container">
    <!-- Toast容器 -->
    <ToastContainer />

    <!-- 现代化顶部头部 -->
    <view class="modern-header">
      <view class="header-background">
        <view class="header-gradient"></view>
        <view class="header-pattern"></view>
      </view>
      <view class="header-content">
        <view class="header-left">
          <view class="app-logo">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
          </view>
          <view class="header-info">
            <text class="app-title">辐射监测中心</text>
            <text class="app-subtitle">{{ currentTime }} • 实时监控</text>
          </view>
        </view>
        <view class="header-right">
          <view class="status-indicators">
            <view class="connection-status" :class="{ connected: deviceState.isConnected }">
              <view class="status-dot"></view>
            </view>
            <view class="notification-button" @click="navigateToNotification">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              <view class="notification-badge" v-if="hasUnreadNotifications">{{ unreadCount }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 重新设计的实时辐射监测卡片 -->
    <view class="radiation-monitor-card">
      <view class="card-header">
        <view class="header-left">
          <view class="monitor-icon" :class="radiationLevelClass">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-6.5l-4.24 4.24M7.76 7.76L3.52 3.52m12.96 12.96l-4.24-4.24M7.76 16.24l-4.24 4.24"></path>
            </svg>
          </view>
          <view class="header-info">
            <text class="card-title">实时辐射监测</text>
            <text class="card-subtitle">{{ radiationStatusText }} • 实时更新</text>
          </view>
        </view>
        <view class="status-indicator" :class="radiationLevelClass">
          <text class="status-text">{{ radiationIcon }}</text>
        </view>
      </view>

      <view class="monitor-content">
        <view class="primary-reading">
          <view class="reading-section">
            <text class="reading-label">剂量率</text>
            <view class="reading-display">
              <text class="reading-value">{{ formatDoseRate(radiationState.current.doseRate) }}</text>
              <text class="reading-unit">μSv/h</text>
            </view>
            <view class="reading-trend" :class="currentTrend.class">
              <text class="trend-icon">{{ currentTrend.icon }}</text>
              <text class="trend-text">{{ currentTrend.text }}</text>
            </view>
          </view>
        </view>

        <view class="secondary-readings">
          <view class="reading-item">
            <view class="item-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"></path>
              </svg>
            </view>
            <view class="item-content">
              <text class="item-label">计数率</text>
              <text class="item-value">{{ radiationState.current.cps }} CPS</text>
            </view>
          </view>

          <view class="reading-item">
            <view class="item-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
            </view>
            <view class="item-content">
              <text class="item-label">累积剂量</text>
              <text class="item-value">{{ (radiationState.current.doseRate * 24).toFixed(2) }} μSv</text>
            </view>
          </view>

          <view class="reading-item">
            <view class="item-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z"></path>
              </svg>
            </view>
            <view class="item-content">
              <text class="item-label">环境温度</text>
              <text class="item-value">{{ radiationState.currentData.temperature.toFixed(1) }}°C</text>
            </view>
          </view>
        </view>

        <view class="monitor-actions">
          <view class="action-button" @click="refreshData">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
              <path d="M21 3v5h-5"></path>
              <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
              <path d="M3 21v-5h5"></path>
            </svg>
            <text>刷新</text>
          </view>
          <view class="action-button" @click="goToCharts">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 3v18h18"></path>
              <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
            </svg>
            <text>详情</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 设备状态摘要 - 参考home.html的device-summary设计 -->
    <view class="device-summary">
      <view class="summary-item">
        <view class="summary-value">{{ radiationState.currentData.cps.toFixed(1) }}</view>
        <view class="summary-label">计数率 CPS</view>
      </view>
      <view class="summary-item">
        <view class="summary-value">{{ radiationState.currentData.doseSum.toFixed(2) }}</view>
        <view class="summary-label">累积剂量 μSv</view>
      </view>
      <view class="summary-item">
        <view class="summary-value">{{ radiationState.currentData.temperature.toFixed(1) }}</view>
        <view class="summary-label">环境温度 °C</view>
      </view>
      <view class="summary-item">
        <view class="summary-circle">
          <view :class="['status-dot', deviceState.connection.mqtt ? '' : 'error']"></view>
        </view>
        <view class="summary-label">设备状态</view>
      </view>
    </view>

    <!-- 滚动内容区域 -->
    <scroll-view class="room-cards" scroll-y="true">
      <view v-if="lastUpdateTime" class="last-update-time">
        最后更新: {{ lastUpdateTime }}
      </view>

      <!-- 实时数据趋势分析 -->
      <view class="section-title">
        <view class="title-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
          </svg>
        </view>
        <view class="title-content">
          <text class="title-main">实时数据趋势分析</text>
          <text class="title-sub">智能可视化 • 趋势预测</text>
        </view>
        <view @click="refreshData" class="refresh-button">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M23 4v6h-6"></path>
            <path d="M1 20v-6h6"></path>
            <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
          </svg>
        </view>
      </view>

      <!-- 高级图表组件 -->
      <!-- 时间筛选器 -->
      <view class="time-filter-container">
        <view class="section-title">
          📊 监测数据
          <text class="section-subtitle">选择时间范围</text>
        </view>
        <view class="time-filter-tabs">
          <view
            v-for="(period, index) in timePeriods"
            :key="index"
            class="time-tab"
            :class="{ active: selectedPeriod === period.value }"
            @click="selectTimePeriod(period.value)"
          >
            {{ period.label }}
          </view>
        </view>
      </view>

      <AdvancedChart
        :title="'剂量率监测'"
        :subtitle="'实时数据 • 5%误差带'"
        :data="chartData"
        :canvas-id="'doseRateChart'"
        :chart-width="contentWidth"
        :chart-height="260"
        :show-error-band="true"
      />

      <AdvancedChart
        :title="'计数率监测'"
        :subtitle="'CPS数据 • 统计分析'"
        :data="cpsChartData"
        :canvas-id="'cpsChart'"
        :chart-width="contentWidth"
        :chart-height="260"
        :show-error-band="true"
      />

      <!-- 剂量率超标时间段网格 -->
      <view class="alert-grid-container">
        <view class="section-title">
          ⚠️ 剂量率超标记录
          <text class="section-subtitle">{{ selectedPeriod === '24h' ? '最近24小时' : selectedPeriod === '7d' ? '最近7天' : '最近30天' }}</text>
        </view>
        <view class="alert-matrix" id="alertMatrix">
          <view
            v-for="(cell, index) in alertMatrixCells"
            :key="index"
            class="matrix-cell"
            :class="{
              'alert': cell.isAlert,
              'normal': !cell.isAlert && cell.hasData,
              'no-data': !cell.hasData
            }"
            @click="showCellDetails(cell)"
          >
            <text class="cell-time">{{ cell.timeLabel }}</text>
            <text class="cell-value" v-if="cell.hasData">{{ cell.value }}</text>
          </view>
        </view>
        <view class="matrix-legend">
          <view class="legend-item">
            <view class="legend-color alert"></view>
            <text class="legend-text">超标</text>
          </view>
          <view class="legend-item">
            <view class="legend-color normal"></view>
            <text class="legend-text">正常</text>
          </view>
          <view class="legend-item">
            <view class="legend-color no-data"></view>
            <text class="legend-text">无数据</text>
          </view>
        </view>
      </view>

      <!-- 重新设计的实时数据卡片 -->
      <view class="modern-data-grid">
        <view class="data-card-modern primary-card">
          <view class="card-background">
            <view class="card-gradient"></view>
            <view class="card-pattern"></view>
          </view>
          <view class="card-header-modern">
            <view class="icon-wrapper primary">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
            </view>
            <view class="card-info">
              <text class="card-title-modern">剂量率监测</text>
              <text class="card-subtitle">实时辐射水平</text>
            </view>
            <view class="status-indicator" :class="{ 'normal': radiationState.currentData.doseRate < 1.0, 'warning': radiationState.currentData.doseRate >= 1.0 }"></view>
          </view>
          <view class="card-content-modern">
            <view class="value-display">
              <text class="main-value">{{ formatDoseRate(radiationState.currentData.doseRate) }}</text>
              <text class="value-unit">μSv/h</text>
            </view>
            <view class="trend-indicator">
              <view class="trend-icon">{{ currentTrend.icon }}</view>
              <text class="trend-text">{{ currentTrend.text }}</text>
            </view>
          </view>
        </view>

        <view class="data-card-modern secondary-card">
          <view class="card-background">
            <view class="card-gradient"></view>
            <view class="card-pattern"></view>
          </view>
          <view class="card-header-modern">
            <view class="icon-wrapper secondary">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
            </view>
            <view class="card-info">
              <text class="card-title-modern">计数率统计</text>
              <text class="card-subtitle">粒子检测频率</text>
            </view>
            <view class="status-indicator normal"></view>
          </view>
          <view class="card-content-modern">
            <view class="value-display">
              <text class="main-value">{{ radiationState.currentData.cps }}</text>
              <text class="value-unit">CPS</text>
            </view>
            <view class="trend-indicator">
              <view class="trend-icon">📊</view>
              <text class="trend-text">稳定运行</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 今日统计卡片 -->
      <view class="section-title">
        📊 今日数据统计
        <text class="section-subtitle">平均值 • 峰值分析</text>
      </view>

      <view class="device-cards-container">
        <view class="device-card sensor animate-slideUp" style="animation-delay: 0.1s">
          <view class="device-details">
            <view>
              <view class="device-icon settings">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12 20V10"></path>
                  <path d="M18 20V4"></path>
                  <path d="M6 20v-6"></path>
                </svg>
              </view>
              <view class="device-name">平均剂量率</view>
            </view>
            <view>
              <view class="device-status sensor-value">
                {{ todayAvgDoseRate.toFixed(2) }} μSv/h
              </view>
              <view class="device-update-time">今日平均</view>
            </view>
          </view>
        </view>

        <view class="device-card sensor animate-slideUp" style="animation-delay: 0.2s">
          <view class="device-details">
            <view>
              <view class="device-icon plus">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
              </view>
              <view class="device-name">最高剂量率</view>
            </view>
            <view>
              <view class="device-status sensor-value">
                {{ todayMaxDoseRate.toFixed(2) }} μSv/h
              </view>
              <view class="device-update-time">今日峰值</view>
            </view>
          </view>
        </view>

        <view class="device-card" :class="todayAlerts > 0 ? 'running' : 'stopped'" style="animation-delay: 0.3s">
          <view class="device-details">
            <view>
              <view class="device-icon star">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                </svg>
              </view>
              <view class="device-name">报警状态</view>
            </view>
            <view>
              <view :class="['device-status', todayAlerts > 0 ? 'running' : 'stopped']">
                {{ todayAlerts > 0 ? `${todayAlerts}次报警` : '正常' }}
              </view>
              <view class="device-update-time" v-if="lastAlert">
                {{ formatTime(lastAlert.timestamp) }}
              </view>
            </view>
          </view>
        </view>

        <view class="device-card" :class="deviceState.connection.mqtt ? 'running' : 'stopped'" style="animation-delay: 0.4s">
          <view class="device-details">
            <view>
              <view class="device-icon refresh">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M23 4v6h-6"></path>
                  <path d="M1 20v-6h6"></path>
                  <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                </svg>
              </view>
              <view class="device-name">设备电量</view>
            </view>
            <view>
              <view :class="['device-status', deviceState.battery.level > 20 ? 'running' : 'stopped']">
                {{ deviceState.battery.level.toFixed(1) }}%
              </view>
              <view class="device-update-time">电池状态</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 快捷操作 -->
      <view class="section-title">
        🎛️ 快捷操作
        <text class="section-subtitle">数据管理</text>
      </view>
      <view class="mode-cards-container">
        <view class="mode-card" @click="goToCharts">
          <view class="mode-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </view>
          <view class="mode-name">历史图表</view>
        </view>
        <view class="mode-card" @click="exportData">
          <view class="mode-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="9" y1="9" x2="15" y2="15"></line>
              <line x1="15" y1="9" x2="9" y2="15"></line>
            </svg>
          </view>
          <view class="mode-name">导出数据</view>
        </view>
        <view class="mode-card" @click="goToMap">
          <view class="mode-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 16v-4"></path>
              <path d="M12 8h.01"></path>
            </svg>
          </view>
          <view class="mode-name">位置信息</view>
        </view>
        <view class="mode-card" @click="navigateToSettings">
          <view class="mode-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 20V10"></path>
              <path d="M18 20V4"></path>
              <path d="M6 20v-6"></path>
            </svg>
          </view>
          <view class="mode-name">系统设置</view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部导航栏 -->
    <BottomNavigation currentPage="dashboard" />
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState, deviceState, locationState } from '../../utils/dataStore.js'
import mqttService from '../../utils/mqttService.js'
import dataStore from '../../utils/dataStore.js'
import ToastContainer from '../../components/ToastContainer.vue'
import AdvancedChart from '../../components/AdvancedChart.vue'
import toastManager from '../../utils/toastManager.js'
import BottomNavigation from '../../components/BottomNavigation.vue'

export default {
  name: 'Dashboard',
  components: {
    ToastContainer,
    AdvancedChart,
    BottomNavigation
  },
  setup() {
    const currentTime = ref('')
    const isCollecting = ref(true)
    const showConnectionStatus = ref(false)
    const timeInterval = ref(null)
    const chartContext = ref(null)
    const lastUpdateTime = ref('')
    const contentWidth = ref(348)

    // 通知相关
    const hasUnreadNotifications = ref(true)
    const unreadCount = ref(3)

    // 导航栏状态
    const activeTabIndex = ref(0)

    const setActiveTab = (index) => {
      activeTabIndex.value = index
    }

    // 统一的导航函数（支持tabBar与普通页面）
    const navigateTo = (page) => {
      const routes = {
        'dashboard': '/pages/dashboard/dashboard',
        'charts': '/pages/charts/charts',
        'health': '/pages/health/health',
        'map': '/pages/map/map',
        'settings': '/pages/settings/settings',
        'notification': '/pages/notification/notification'
      }
      const tabBarPages = ['dashboard', 'charts', 'health', 'map', 'settings']

      if (routes[page]) {
        uni.navigateTo({
          url: routes[page],
          fail: (err) => {
            console.error('导航失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      }
    }

    // 图表数据
    const chartData = computed(() => {
      return radiationState.history.map(item => ({
        value: item.doseRate,
        timestamp: item.timestamp,
        time: item.timestamp
      }))
    })

    const cpsChartData = computed(() => {
      return radiationState.history.map(item => ({
        value: item.cps,
        timestamp: item.timestamp,
        time: item.timestamp
      }))
    })

    // 导航项
    const navItems = ref([
      { text: '实时', emoji: '📊' },
      { text: '图表', emoji: '📈' },
      { text: '地图', emoji: '🗺️' },
      { text: '设置', emoji: '⚙️' }
    ])

    // 计算属性
    const radiationLevel = computed(() => {
      const doseRate = radiationState.currentData.doseRate
      const { maxDoseRate, minDoseRate } = radiationState.settings
      
      if (doseRate > maxDoseRate) return 'danger'
      if (doseRate < minDoseRate) return 'warning'
      return 'safe'
    })

    const radiationLevelClass = computed(() => {
      return `level-${radiationLevel.value}`
    })

    const radiationIcon = computed(() => {
      switch (radiationLevel.value) {
        case 'danger': return '🚨'
        case 'warning': return '⚠️'
        default: return '✅'
      }
    })

    const radiationStatusText = computed(() => {
      switch (radiationLevel.value) {
        case 'danger': return '危险水平'
        case 'warning': return '需要注意'
        default: return '安全水平'
      }
    })

    // 今日统计数据
    const todayAvgDoseRate = computed(() => {
      const today = new Date().toDateString()
      const todayData = radiationState.history.filter(item => 
        new Date(item.timestamp).toDateString() === today
      )
      
      if (todayData.length === 0) return 0
      
      const total = todayData.reduce((sum, item) => sum + item.doseRate, 0)
      return total / todayData.length
    })

    const todayMaxDoseRate = computed(() => {
      const today = new Date().toDateString()
      const todayData = radiationState.history.filter(item => 
        new Date(item.timestamp).toDateString() === today
      )
      
      if (todayData.length === 0) return 0
      
      return Math.max(...todayData.map(item => item.doseRate))
    })

    const todayAlerts = computed(() => {
      const today = new Date().toDateString()
      return radiationState.alerts.filter(alert => 
        new Date(alert.timestamp).toDateString() === today
      ).length
    })

    const lastAlert = computed(() => {
      return radiationState.alerts.length > 0 ? radiationState.alerts[0] : null
    })

    // 方法
    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const formatDoseRate = (value) => {
      // 统一显示最多两位小数
      return value.toFixed(2)
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const toggleCollection = () => {
      isCollecting.value = !isCollecting.value
      toastManager.success(isCollecting.value ? '开始采集数据' : '停止采集数据')
    }

    const exportData = () => {
      try {
        dataStore.exportData()

        uni.showModal({
          title: '导出数据',
          content: '数据已准备就绪，您可以将其保存到文件',
          confirmText: '确定',
          success: (res) => {
            if (res.confirm) {
              toastManager.success('导出成功')
            }
          }
        })
      } catch (error) {
        toastManager.error('导出失败')
      }
    }

    const showSettings = () => {
      uni.navigateTo({
        url: '/pages/settings/settings'
      })
    }

    const navigateToNotification = () => {
      uni.navigateTo({
        url: '/pages/notification/notification'
      })
    }

    const navigateToSettings = () => {
      uni.navigateTo({
        url: '/pages/settings/settings'
      })
    }

    const goToCharts = () => {
      uni.navigateTo({
        url: '/pages/charts/charts'
      })
    }

    const goToMap = () => {
      uni.navigateTo({
        url: '/pages/map/map'
      })
    }



    const refreshData = () => {
      lastUpdateTime.value = new Date().toLocaleTimeString()
      toastManager.info('数据已刷新')
    }

    // 跳转到图表页面
    const goToCharts = () => {
      uni.navigateTo({
        url: '/pages/charts/charts'
      })
    }

    // 时间筛选器方法
    const selectTimePeriod = (period) => {
      selectedPeriod.value = period
      generateAlertMatrix()
      toastManager.info(`已切换到${timePeriods.value.find(p => p.value === period)?.label}视图`)
    }

    // 生成警报矩阵
    const generateAlertMatrix = () => {
      const cells = []
      const now = new Date()
      let timeRange, cellCount, intervalMs

      switch (selectedPeriod.value) {
        case '24h':
          timeRange = 24 * 60 * 60 * 1000 // 24小时
          cellCount = 24 // 24个小时
          intervalMs = 60 * 60 * 1000 // 1小时间隔
          break
        case '7d':
          timeRange = 7 * 24 * 60 * 60 * 1000 // 7天
          cellCount = 28 // 4周
          intervalMs = 6 * 60 * 60 * 1000 // 6小时间隔
          break
        case '30d':
          timeRange = 30 * 24 * 60 * 60 * 1000 // 30天
          cellCount = 30 // 30天
          intervalMs = 24 * 60 * 60 * 1000 // 1天间隔
          break
        default:
          timeRange = 24 * 60 * 60 * 1000
          cellCount = 24
          intervalMs = 60 * 60 * 1000
      }

      const startTime = new Date(now.getTime() - timeRange)

      for (let i = 0; i < cellCount; i++) {
        const cellStartTime = new Date(startTime.getTime() + i * intervalMs)
        const cellEndTime = new Date(cellStartTime.getTime() + intervalMs)

        // 模拟数据 - 在实际应用中应该从历史数据中获取
        const hasData = Math.random() > 0.1 // 90%的时间有数据
        const value = hasData ? (Math.random() * 2).toFixed(2) : null
        const isAlert = hasData && parseFloat(value) > radiationState.settings.maxDoseRate

        let timeLabel
        if (selectedPeriod.value === '24h') {
          timeLabel = cellStartTime.getHours().toString().padStart(2, '0') + ':00'
        } else if (selectedPeriod.value === '7d') {
          timeLabel = (cellStartTime.getMonth() + 1) + '/' + cellStartTime.getDate()
        } else {
          timeLabel = (cellStartTime.getMonth() + 1) + '/' + cellStartTime.getDate()
        }

        cells.push({
          index: i,
          startTime: cellStartTime,
          endTime: cellEndTime,
          timeLabel,
          hasData,
          value,
          isAlert
        })
      }

      alertMatrixCells.value = cells
    }

    // 显示单元格详情
    const showCellDetails = (cell) => {
      if (!cell.hasData) {
        toastManager.info('该时间段无数据')
        return
      }

      const timeRange = `${cell.startTime.toLocaleString()} - ${cell.endTime.toLocaleString()}`
      const status = cell.isAlert ? '超标' : '正常'
      const message = `时间: ${timeRange}\n剂量率: ${cell.value} μSv/h\n状态: ${status}`

      if (cell.isAlert) {
        toastManager.warning(message, { duration: 5000 })
      } else {
        toastManager.info(message, { duration: 4000 })
      }
    }

    // 新增的交互方法
    const getBatteryLevelClass = () => {
      const level = deviceState.battery.level
      if (level > 60) return 'high'
      if (level > 30) return 'medium'
      return 'low'
    }

    const getStatProgress = (value) => {
      return Math.min((value / 0.5) * 100, 100) // 假设0.5为最大参考值
    }

    const viewDetailedStats = () => {
      uni.navigateTo({
        url: '/pages/charts/charts'
      })
    }

    const viewAlerts = () => {
      uni.showActionSheet({
        itemList: ['查看所有报警', '报警设置', '清除历史'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              toastManager.info('查看报警列表')
              break
            case 1:
              uni.navigateTo({ url: '/pages/settings/settings' })
              break
            case 2:
              toastManager.info('清除历史记录')
              break
          }
        }
      })
    }

    const refreshChart = () => {
      isChartLoading.value = true
      setTimeout(() => {
        drawMiniChart()
        isChartLoading.value = false
      }, 1000)
    }

    const showQuickMenu = () => {
      uni.showActionSheet({
        itemList: ['数据导出', '设备设置', '系统信息', '帮助中心'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              exportData()
              break
            case 1:
              showSettings()
              break
            case 2:
              showConnectionStatus.value = true
              break
            case 3:
              toastManager.info('帮助中心')
              break
          }
        }
      })
    }

    const hideConnectionStatus = () => {
      showConnectionStatus.value = false
    }

    const reconnectDevice = () => {
      uni.showLoading({ title: '重新连接中...' })
      setTimeout(() => {
        uni.hideLoading()
        toastManager.success('连接成功')
        hideConnectionStatus()
      }, 2000)
    }

    const testConnection = () => {
      uni.showLoading({ title: '测试连接中...' })
      setTimeout(() => {
        uni.hideLoading()
        toastManager.success('连接正常')
      }, 1500)
    }

    const initMiniChart = () => {
      // 初始化迷你图表
      const ctx = uni.createCanvasContext('realtimeChart')
      if (!ctx) return

      chartContext.value = ctx
      drawMiniChart()
    }

    const drawMiniChart = () => {
      if (!chartContext.value) return

      const ctx = chartContext.value
      const width = 300
      const height = 120
      const padding = 20
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      // 清空画布
      ctx.clearRect(0, 0, width, height)

      // 获取最近的数据点
      const recentData = radiationState.history.slice(0, 30).reverse()

      // 如果没有数据，创建模拟数据用于演示
      if (recentData.length < 2) {
        const mockData = []
        for (let i = 0; i < 20; i++) {
          mockData.push({
            doseRate: 0.1 + Math.sin(i * 0.3) * 0.05 + Math.random() * 0.02,
            cps: 50 + Math.sin(i * 0.4) * 15 + Math.random() * 10,
            timestamp: new Date(Date.now() - (19 - i) * 60000)
          })
        }
        recentData.splice(0, 0, ...mockData)
      }

      // 找出最大值和最小值用于缩放
      const doseValues = recentData.map(item => item.doseRate)
      const cpsValues = recentData.map(item => item.cps)
      const maxDose = Math.max(...doseValues)
      const minDose = Math.min(...doseValues)
      const maxCps = Math.max(...cpsValues)
      const minCps = Math.min(...cpsValues)
      const doseRange = maxDose - minDose || 0.1
      const cpsRange = maxCps - minCps || 20

      // 绘制背景网格 - 参考history.html样式
      ctx.setStrokeStyle('rgba(0, 0, 0, 0.05)')
      ctx.setLineWidth(0.5)
      for (let i = 1; i < 5; i++) {
        const y = padding + (i * chartHeight / 5)
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }

      // 绘制剂量率曲线 - 使用蓝色渐变，参考Chart.js样式
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)

      recentData.forEach((item, index) => {
        const x = padding + (index / (recentData.length - 1)) * chartWidth
        const y = height - padding - ((item.doseRate - minDose) / doseRange) * chartHeight

        if (index === 0) {
          ctx.lineTo(x, y)
        } else {
          // 使用贝塞尔曲线创建平滑效果
          const prevX = padding + ((index - 1) / (recentData.length - 1)) * chartWidth
          const prevY = height - padding - ((recentData[index - 1].doseRate - minDose) / doseRange) * chartHeight
          const cpX = (prevX + x) / 2
          ctx.bezierCurveTo(cpX, prevY, cpX, y, x, y)
        }
      })

      ctx.lineTo(width - padding, height - padding)

      // 创建渐变填充 - 蓝色主题
      const gradient1 = ctx.createLinearGradient(0, padding, 0, height - padding)
      gradient1.addColorStop(0, 'rgba(2, 136, 209, 0.8)')
      gradient1.addColorStop(1, 'rgba(2, 136, 209, 0.1)')
      ctx.setFillStyle(gradient1)
      ctx.fill()

      // 绘制剂量率线条
      ctx.beginPath()
      ctx.setStrokeStyle('#0288D1')
      ctx.setLineWidth(3)
      ctx.setLineCap('round')
      ctx.setLineJoin('round')

      recentData.forEach((item, index) => {
        const x = padding + (index / (recentData.length - 1)) * chartWidth
        const y = height - padding - ((item.doseRate - minDose) / doseRange) * chartHeight

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          const prevX = padding + ((index - 1) / (recentData.length - 1)) * chartWidth
          const prevY = height - padding - ((recentData[index - 1].doseRate - minDose) / doseRange) * chartHeight
          const cpX = (prevX + x) / 2
          ctx.bezierCurveTo(cpX, prevY, cpX, y, x, y)
        }
      })
      ctx.stroke()

      // 绘制计数率曲线 - 使用绿色渐变
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)

      recentData.forEach((item, index) => {
        const x = padding + (index / (recentData.length - 1)) * chartWidth
        const y = height - padding - ((item.cps - minCps) / cpsRange) * chartHeight * 0.7

        if (index === 0) {
          ctx.lineTo(x, y)
        } else {
          const prevX = padding + ((index - 1) / (recentData.length - 1)) * chartWidth
          const prevY = height - padding - ((recentData[index - 1].cps - minCps) / cpsRange) * chartHeight * 0.7
          const cpX = (prevX + x) / 2
          ctx.bezierCurveTo(cpX, prevY, cpX, y, x, y)
        }
      })

      ctx.lineTo(width - padding, height - padding)

      // 创建渐变填充 - 绿色主题
      const gradient2 = ctx.createLinearGradient(0, padding, 0, height - padding)
      gradient2.addColorStop(0, 'rgba(76, 175, 80, 0.6)')
      gradient2.addColorStop(1, 'rgba(76, 175, 80, 0.1)')
      ctx.setFillStyle(gradient2)
      ctx.fill()

      // 绘制计数率线条
      ctx.beginPath()
      ctx.setStrokeStyle('#4CAF50')
      ctx.setLineWidth(3)
      ctx.setLineCap('round')
      ctx.setLineJoin('round')

      recentData.forEach((item, index) => {
        const x = padding + (index / (recentData.length - 1)) * chartWidth
        const y = height - padding - ((item.cps - minCps) / cpsRange) * chartHeight * 0.7

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          const prevX = padding + ((index - 1) / (recentData.length - 1)) * chartWidth
          const prevY = height - padding - ((recentData[index - 1].cps - minCps) / cpsRange) * chartHeight * 0.7
          const cpX = (prevX + x) / 2
          ctx.bezierCurveTo(cpX, prevY, cpX, y, x, y)
        }
      })
      ctx.stroke()

      // 绘制数据点 - 只显示最后几个点
      const pointsToShow = Math.min(5, recentData.length)
      for (let i = recentData.length - pointsToShow; i < recentData.length; i++) {
        const x = padding + (i / (recentData.length - 1)) * chartWidth
        const doseY = height - padding - ((recentData[i].doseRate - minDose) / doseRange) * chartHeight
        const cpsY = height - padding - ((recentData[i].cps - minCps) / cpsRange) * chartHeight * 0.7

        // 剂量率数据点 - 蓝色
        ctx.beginPath()
        ctx.arc(x, doseY, 4, 0, 2 * Math.PI)
        ctx.setFillStyle('#ffffff')
        ctx.fill()
        ctx.beginPath()
        ctx.arc(x, doseY, 3, 0, 2 * Math.PI)
        ctx.setFillStyle('#0288D1')
        ctx.fill()

        // 计数率数据点 - 绿色
        ctx.beginPath()
        ctx.arc(x, cpsY, 4, 0, 2 * Math.PI)
        ctx.setFillStyle('#ffffff')
        ctx.fill()
        ctx.beginPath()
        ctx.arc(x, cpsY, 3, 0, 2 * Math.PI)
        ctx.setFillStyle('#4CAF50')
        ctx.fill()
      }

      ctx.draw()
    }

    // 辐射监测警告逻辑
    const checkRadiationLevels = () => {
      const doseRate = radiationState.currentData.doseRate
      const { maxDoseRate, minDoseRate } = radiationState.settings

      // 检查剂量率是否超标
      if (doseRate > maxDoseRate) {
        toastManager.error(`剂量率过高: ${formatDoseRate(doseRate)} μSv/h`, {
          duration: 5000
        })
      } else if (doseRate < minDoseRate) {
        toastManager.warning(`剂量率异常偏低: ${formatDoseRate(doseRate)} μSv/h`, {
          duration: 4000
        })
      }

      // 检查计数率异常
      const cps = radiationState.currentData.cps
      if (cps > 1000) {
        toastManager.warning(`计数率异常: ${cps} CPS`, {
          duration: 4000
        })
      }

      // 检查设备连接状态
      if (!deviceState.connection.mqtt) {
        toastManager.error('设备连接中断，请检查网络连接', {
          duration: 6000
        })
      }

      // 检查电池电量
      if (deviceState.battery.level < 20) {
        toastManager.warning(`设备电量不足: ${deviceState.battery.level}%`, {
          duration: 4000
        })
      }
    }

    // 生命周期
    onMounted(() => {
      updateTime()
      timeInterval.value = setInterval(updateTime, 1000)

      // 初始化MQTT连接
      mqttService.connect()

      // 注册MQTT事件监听
      mqttService.onMessage('radiationData', (data) => {
        dataStore.updateRadiationData(data)
        drawMiniChart()
        // 检查辐射水平
        checkRadiationLevels()
      })

      mqttService.onMessage('deviceStatus', (data) => {
        dataStore.updateDeviceStatus(data)
        // 检查设备状态
        checkRadiationLevels()
      })

      mqttService.onMessage('connected', () => {
        deviceState.connection.mqtt = true
        toastManager.success('设备连接成功')
      })

      mqttService.onMessage('disconnected', () => {
        deviceState.connection.mqtt = false
        toastManager.error('设备连接中断')
      })

      // 初始化图表
      setTimeout(() => {
        initMiniChart()
      }, 500)

      // 定期检查辐射水平（每30秒）
      setInterval(checkRadiationLevels, 30000)

      // 初始化警报矩阵
      generateAlertMatrix()

      // 模拟数据更新（仅用于演示）
      const simulateData = () => {
        const mockData = {
          doseRate: 0.1 + Math.random() * 0.05,
          cps: 50 + Math.random() * 20,
          doseSum: radiationState.currentData.doseSum + Math.random() * 0.001,
          alarmStatus: Math.random() > 0.95 ? 2 : 0,
          temperature: 25 + Math.random() * 5
        }
        dataStore.updateRadiationData(mockData)
        drawMiniChart()
      }

      // 每5秒更新一次模拟数据
      const dataInterval = setInterval(simulateData, 5000)
      
      onUnmounted(() => {
        clearInterval(dataInterval)
      })
    })

    onUnmounted(() => {
      if (timeInterval.value) {
        clearInterval(timeInterval.value)
      }
      mqttService.disconnect()
    })

         return {
       currentTime,
       isCollecting,
       showConnectionStatus,
       radiationState,
       deviceState,
       locationState,
       radiationLevelClass,
       radiationIcon,
       radiationStatusText,
       todayAvgDoseRate,
       todayMaxDoseRate,
       todayAlerts,
       lastAlert,
       formatDoseRate,
       formatTime,
       toggleCollection,
       exportData,
       showSettings,
       goToCharts,
       hideConnectionStatus,
       getBatteryLevelClass,
       getStatProgress,
       viewDetailedStats,
       viewAlerts,
       refreshChart,
       showQuickMenu,
       reconnectDevice,
       testConnection,
       // 新增的方法和数据
       lastUpdateTime,
       navItems,
       navigateTo,
       navigateToSettings,
       navigateToNotification,
       goToMap,
       refreshData,
       hasUnreadNotifications,
       unreadCount,
       activeTabIndex,
       setActiveTab,
       chartData,
       cpsChartData,
       contentWidth,
       isChartLoading: ref(false),
       // 时间筛选器相关
       selectedPeriod: ref('24h'),
       timePeriods: ref([
         { label: '24小时', value: '24h' },
         { label: '7天', value: '7d' },
         { label: '30天', value: '30d' }
       ]),
       alertMatrixCells: ref([]),
       selectTimePeriod,
       showCellDetails,
       goToCharts,
       dataPointsCount: computed(() => radiationState.history.length),
       currentTrend: computed(() => {
         const recent = radiationState.history.slice(0, 10)
         if (recent.length < 2) return { icon: '➖', text: '无数据', class: 'neutral' }
         const current = recent[0].doseRate
         const previous = recent[recent.length - 1].doseRate
         const change = ((current - previous) / previous) * 100
         if (Math.abs(change) < 1) return { icon: '➖', text: '稳定', class: 'stable' }
         if (change > 0) return { icon: '📈', text: `+${change.toFixed(1)}%`, class: 'up' }
         return { icon: '📉', text: `${change.toFixed(1)}%`, class: 'down' }
       })
     }
  }
}
</script>

<style scoped>
/* 修复整体样式，确保页面撑满屏幕 - 参考home.html */
page {
    background: #ffffff;
    background-image: 
        radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

.container {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0 0 120px 0; /* 增加底部内边距，避免被导航栏遮挡 */
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(120deg, #e3f0ff 0%, #f7f7f7 100%);
    position: relative;
    min-height: 100vh;
    overflow-y: auto; /* 改为可滚动 */
    overflow-x: hidden;
    box-sizing: border-box;
}

/* 重新设计的实时辐射监测卡片 */
.radiation-monitor-card {
    margin: 16px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.8);
    overflow: hidden;
}

.radiation-monitor-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.radiation-monitor-card .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.radiation-monitor-card .monitor-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 2px solid rgba(33, 150, 243, 0.2);
}

.radiation-monitor-card .monitor-icon svg {
    width: 24px;
    height: 24px;
    color: #2196F3;
}

.radiation-monitor-card .monitor-icon.level-danger {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    border-color: rgba(244, 67, 54, 0.3);
}

.radiation-monitor-card .monitor-icon.level-danger svg {
    color: #f44336;
}

.radiation-monitor-card .monitor-icon.level-warning {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    border-color: rgba(255, 152, 0, 0.3);
}

.radiation-monitor-card .monitor-icon.level-warning svg {
    color: #ff9800;
}

.radiation-monitor-card .header-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.radiation-monitor-card .card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
}

.radiation-monitor-card .card-subtitle {
    font-size: 13px;
    color: #666;
    font-weight: 500;
}

.radiation-monitor-card .status-indicator {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.radiation-monitor-card .status-indicator.level-danger {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.radiation-monitor-card .status-indicator.level-warning {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.radiation-monitor-card .status-text {
    font-size: 18px;
}

.radiation-monitor-card .monitor-content {
    padding: 20px;
}

.radiation-monitor-card .primary-reading {
    margin-bottom: 24px;
}

.radiation-monitor-card .reading-section {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.radiation-monitor-card .reading-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    margin-bottom: 8px;
}

.radiation-monitor-card .reading-display {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 8px;
    margin-bottom: 12px;
}

.radiation-monitor-card .reading-value {
    font-size: 36px;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1;
}

.radiation-monitor-card .reading-unit {
    font-size: 16px;
    color: #666;
    font-weight: 500;
}

.radiation-monitor-card .reading-trend {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.radiation-monitor-card .trend-icon {
    font-size: 14px;
}

.radiation-monitor-card .trend-text {
    font-size: 12px;
    font-weight: 600;
    color: #666;
}

.radiation-monitor-card .secondary-readings {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 20px;
}

.radiation-monitor-card .reading-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.radiation-monitor-card .item-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: rgba(33, 150, 243, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.radiation-monitor-card .item-icon svg {
    width: 16px;
    height: 16px;
    color: #2196F3;
}

.radiation-monitor-card .item-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.radiation-monitor-card .item-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.radiation-monitor-card .item-value {
    font-size: 14px;
    color: #1a1a1a;
    font-weight: 600;
}

.radiation-monitor-card .monitor-actions {
    display: flex;
    gap: 12px;
}

.radiation-monitor-card .action-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    color: white;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.radiation-monitor-card .action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(33, 150, 243, 0.4);
}

.radiation-monitor-card .action-button:active {
    transform: translateY(0);
}

.radiation-monitor-card .action-button svg {
    width: 16px;
    height: 16px;
}

/* 现代化头部样式 */
.modern-header {
    position: relative;
    width: 100%;
    max-width: 480px;
    padding: 20px;
    margin-bottom: 20px;
    overflow: hidden;
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.header-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 252, 0.9) 100%);
    border-radius: 24px;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.header-pattern {
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle at center,
        rgba(59, 130, 246, 0.1) 0%,
        transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.header-content {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.app-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.app-logo svg {
    width: 24px;
    height: 24px;
    color: #ffffff;
}

.header-info {
    flex: 1;
}

.app-title {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #0f172a;
    line-height: 1.3;
}

.app-subtitle {
    display: block;
    font-size: 14px;
    color: #64748b;
    margin-top: 2px;
    font-weight: 500;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-indicators {
    display: flex;
    align-items: center;
    gap: 12px;
}

.connection-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: relative;
}

.status-dot {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #ef4444;
    transition: all 0.3s ease;
}

.connection-status.connected .status-dot {
    background: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
    animation: pulse-connected 2s infinite;
}

.notification-button {
    position: relative;
    width: 40px;
    height: 40px;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-button:hover {
    background: #3b82f6;
    transform: scale(1.05);
}

.notification-button svg {
    width: 20px;
    height: 20px;
    color: #64748b;
    transition: all 0.3s ease;
}

.notification-button:hover svg {
    color: #ffffff;
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
}

@keyframes pulse-connected {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* 主要辐射数据卡片样式 */
.hero-radiation-card {
    position: relative;
    width: calc(100% - 32px);
    max-width: 480px;
    margin: 0 16px 20px;
    border-radius: 24px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.hero-radiation-card .card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.hero-radiation-card .card-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.95) 0%,
        rgba(29, 78, 216, 0.9) 100%);
}

.radiation-waves {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.wave {
    position: absolute;
    border-radius: 50%;
    border: 1.5px solid rgba(255, 255, 255, 0.15);
    animation: wave-pulse 4s infinite ease-in-out;
}

.wave-1 {
    width: 120px;
    height: 120px;
    top: 15%;
    right: 5%;
    animation-delay: 0s;
}

.wave-2 {
    width: 80px;
    height: 80px;
    top: 55%;
    right: 15%;
    animation-delay: 1.3s;
}

.wave-3 {
    width: 100px;
    height: 100px;
    top: 35%;
    right: 65%;
    animation-delay: 2.6s;
}

.wave-4 {
    width: 60px;
    height: 60px;
    top: 70%;
    right: 50%;
    animation-delay: 3.9s;
}

.card-content {
    position: relative;
    z-index: 1;
    padding: 28px 24px;
    color: #ffffff;
}

.radiation-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
}

.radiation-icon-large {
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.radiation-icon-large svg {
    width: 32px;
    height: 32px;
    color: #ffffff;
}

.radiation-status {
    flex: 1;
}

.status-title {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    line-height: 1.3;
    margin-bottom: 4px;
}

.status-level {
    display: block;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.radiation-data {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.main-reading {
    display: flex;
    align-items: baseline;
    gap: 8px;
}

.reading-value {
    font-size: 48px;
    font-weight: 700;
    color: #ffffff;
    line-height: 1;
}

.reading-unit {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.secondary-data {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.data-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 16px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.data-label {
    display: block;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    margin-bottom: 4px;
}

.data-value {
    display: block;
    font-size: 16px;
    color: #ffffff;
    font-weight: 600;
}

@keyframes wave-pulse {
    0% {
        transform: scale(0.8);
        opacity: 0.4;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.1;
    }
    100% {
        transform: scale(0.8);
        opacity: 0.4;
    }
}

/* SVG图标通用样式 */
.weather-icon svg {
    width: 28px;
    height: 28px;
    color: #f9ad3d;
}

.notification-icon svg {
    width: 20px;
    height: 20px;
    color: white;
}

.device-icon svg {
    width: 28px;
    height: 28px;
    color: #444;
}

.mode-icon svg {
    width: 28px;
    height: 28px;
    color: #444;
}

.nav-icon {
    width: 22px;
    height: 22px;
    margin-bottom: 4px;
    opacity: 0.5;
    transition: all 0.3s ease;
    color: #aaa;
}

/* 顶部状态栏 - 参考home.html设计 */
.status-bar {
    display: flex;
    justify-content: space-between;
    padding: 16px 20px 10px;
    align-items: center;
    position: relative;
    z-index: 10;
    background-color: #f0f0f0;
    width: 100%;
    max-width: 480px;
}

.date-info {
    display: flex;
    flex-direction: column;
}

.date-weather {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.date {
    font-size: 12px;
    color: #999;
}

.weather-icon-small {
    width: 20px;
    height: 20px;
    color: #f9ad3d;
}

.weather-icon-small svg {
    width: 20px;
    height: 20px;
    color: #f9ad3d;
}

.location {
    font-size: 18px;
    font-weight: 600;
    color: #222;
}

.subtitle {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.notification-icon {
    width: 40px;
    height: 40px;
    background-color: #222;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-icon:hover {
    transform: scale(1.05);
}

.notification-icon svg {
    width: 20px;
    height: 20px;
    color: white;
}

/* 天气信息卡片 */
.weather-card {
    background-color: white;
    border-radius: 16px;
    margin: 0 16px 16px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    width: calc(100% - 32px);
    max-width: 448px;
}

.weather-card-title {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin-bottom: 16px;
}

.weather-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.weather-condition {
    display: flex;
    align-items: center;
}

.weather-icon {
    margin-right: 10px;
    display: flex;
    align-items: center;
}

.weather-data {
    display: flex;
    align-items: center;
}

.weather-temp {
    font-size: 16px;
    font-weight: 600;
    margin: 0 6px;
    color: #333;
}

.temp-divider {
    font-size: 16px;
    color: #ddd;
}

/* 设备状态摘要 */
.device-summary {
    display: flex;
    padding: 16px;
    margin: 0 16px 16px;
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    justify-content: space-around;
    width: calc(100% - 32px);
    max-width: 480px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.summary-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.summary-label {
    font-size: 12px;
    color: #999;
    text-align: center;
}

.summary-circle {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #4caf50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
}

.status-dot.warning {
    background-color: #ff9800;
    box-shadow: 0 0 8px rgba(255, 152, 0, 0.6);
}

.status-dot.error {
    background-color: #f44336;
    box-shadow: 0 0 8px rgba(244, 67, 54, 0.6);
}

/* 设备卡片区域 */
.room-cards {
    flex: 1;
    overflow-y: auto;
    padding: 0 0; /* 由内部卡片控制左右间距 */
    margin-bottom: 80px; /* 为底部导航与阴影留出空间 */
    width: 100%;
    max-width: 480px;
    align-self: stretch;
}

.last-update-time {
    text-align: center;
    font-size: 12px;
    color: #64748b;
    margin-bottom: 16px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
}

/* 修复设备卡片容器和卡片样式 */
.device-cards-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin: 16px 16px; /* 统一边距 */
    padding: 0;
}

.device-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-radius: 18px;
    overflow: hidden;
    box-shadow: 0 3px 16px rgba(0, 0, 0, 0.04);
    cursor: pointer;
    height: 150px;
    display: flex;
    flex-direction: column;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.device-card.running {
    border-left: 4px solid #4caf50;
}

.device-card.stopped {
    border-left: 4px solid #f44336;
}

.device-card.sensor {
    border-left: 4px solid #2196f3;
}

.device-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.device-card:active {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

.device-details {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.device-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    background: linear-gradient(145deg, #f0f0f0, #fafafa);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.device-icon svg {
    width: 28px;
    height: 28px;
    color: #444;
}

.device-icon.gear svg {
    color: #ff9800;
}

.device-icon.settings svg {
    color: #2196f3;
}

.device-icon.plus svg {
    color: #4caf50;
}

.device-icon.star svg {
    color: #9c27b0;
}

.device-icon.minus svg {
    color: #f44336;
}

.device-icon.refresh svg {
    color: #00bcd4;
}

.device-icon.info svg {
    color: #607d8b;
}

.device-icon.more svg {
    color: #3f51b5;
}

.device-name {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.device-status {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 10px;
    border-radius: 12px;
    background-color: #f0f0f0;
    display: inline-block;
    width: fit-content;
    text-align: center;
    margin: 0 auto 4px;
}

.device-status.running {
    color: #4caf50;
    background-color: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.2);
}

.device-status.stopped {
    color: #f44336;
    background-color: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.2);
}

.device-status.sensor-value {
    color: #2196f3;
    background-color: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.2);
}

.device-update-time {
    font-size: 10px;
    color: #999;
    margin-top: 4px;
    text-align: center;
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 16px; /* 统一边距 */
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.title-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.title-icon svg {
    width: 18px;
    height: 18px;
    color: #ffffff;
}

.title-content {
    flex: 1;
}

.title-main {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.3;
}

.title-sub {
    display: block;
    font-size: 13px;
    color: #64748b;
    margin-top: 2px;
}

.refresh-button {
    width: 40px;
    height: 40px;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-button:hover {
    background: #3b82f6;
    transform: scale(1.05);
}

.refresh-button svg {
    width: 20px;
    height: 20px;
    color: #64748b;
    transition: all 0.3s ease;
}

.refresh-button:hover svg {
    color: #ffffff;
}

/* 图表容器 */
.chart-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    border-radius: 20px;
    margin-bottom: 24px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00b4d8 0%, #22c55e 100%);
    border-radius: 20px 20px 0 0;
}

.realtime-chart {
    width: 100%;
    height: 120px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.chart-legend {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: relative;
}

.legend-dot.dose-rate {
    background: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);
    box-shadow: 0 2px 8px rgba(0, 180, 216, 0.3);
}

.legend-dot.count-rate {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.legend-text {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
}

/* 修复模式卡片容器和卡片样式 */
.mode-cards-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin: 16px 16px; /* 统一边距 */
    padding: 0;
}

.mode-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-radius: 18px;
    padding: 16px 14px;
    height: 110px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 16px rgba(0, 0, 0, 0.04);
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.mode-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle at center,
        rgba(59, 130, 246, 0.1) 0%,
        transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.mode-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.mode-card:active {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

.mode-card.active {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: 1px solid rgba(59, 130, 246, 0.3);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
}

.mode-card.active .mode-icon svg,
.mode-card.active .mode-text {
    color: #ffffff;
}

.mode-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
}

.mode-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    text-align: center;
}

/* 全新现代底部导航栏 - 参考test文件夹设计 */
.modern-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 0 20px 20px;
}

.nav-background {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.8) 30%,
        rgba(255, 255, 255, 0.95) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.nav-content {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 24px;
    padding: 12px 16px;
    margin: 0 8px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.08),
        0 4px 16px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.nav-tab {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    min-width: 60px;
}

.nav-tab.active {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow:
        0 8px 24px rgba(59, 130, 246, 0.3),
        0 4px 12px rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
}

.nav-tab:hover:not(.active) {
    background: rgba(59, 130, 246, 0.08);
    transform: translateY(-1px);
}

.tab-icon-container {
    position: relative;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
}

.tab-icon {
    width: 22px;
    height: 22px;
    color: #64748b;
    transition: all 0.3s ease;
}

.nav-tab.active .tab-icon {
    color: #ffffff;
    transform: scale(1.1);
}

.tab-label {
    font-size: 11px;
    font-weight: 500;
    color: #64748b;
    transition: all 0.3s ease;
    text-align: center;
    line-height: 1.2;
}

.nav-tab.active .tab-label {
    color: #ffffff;
    font-weight: 600;
}

.notification-dot {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-radius: 50%;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
    animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
}

/* 时间筛选器样式 */
.time-filter-container {
    margin: 16px 16px 20px;
}

.time-filter-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.time-tab {
    flex: 1;
    text-align: center;
    padding: 10px 16px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.time-tab.active {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
    transform: translateY(-1px);
}

.time-tab:hover:not(.active) {
    background: rgba(33, 150, 243, 0.1);
    color: #2196F3;
}

/* 警报矩阵样式 */
.alert-grid-container {
    margin: 16px 16px 20px;
}

.alert-matrix {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 8px;
    margin-bottom: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.matrix-cell {
    aspect-ratio: 1/1;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.matrix-cell:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1;
}

.matrix-cell.alert {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    border-color: rgba(244, 67, 54, 0.5);
    box-shadow: 0 0 0 1px rgba(244, 67, 54, 0.3), 0 2px 4px rgba(244, 67, 54, 0.2);
}

.matrix-cell.normal {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
    border-color: rgba(76, 175, 80, 0.5);
    box-shadow: 0 0 0 1px rgba(76, 175, 80, 0.3), 0 2px 4px rgba(76, 175, 80, 0.2);
}

.matrix-cell.no-data {
    background: #f5f5f5;
    color: #999;
    border-color: #ddd;
    cursor: default;
}

.matrix-cell.no-data:hover {
    transform: scale(1.02);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.cell-time {
    font-size: 9px;
    margin-bottom: 2px;
}

.cell-value {
    font-size: 8px;
    font-weight: 600;
}

.matrix-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 12px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 3px;
}

.legend-color.alert {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.legend-color.normal {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.legend-color.no-data {
    background: #f5f5f5;
    border: 1px solid #ddd;
}

.legend-text {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

/* 现代化数据卡片网格 - 优化间距和比例 */
.modern-data-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin: 16px 16px;
    padding: 0;
}

.data-card-modern {
    position: relative;
    border-radius: 18px;
    padding: 16px;
    min-height: 140px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 6px 24px rgba(0, 0, 0, 0.06),
        0 2px 8px rgba(0, 0, 0, 0.03);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.data-card-modern:hover {
    transform: translateY(-4px);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.12),
        0 8px 24px rgba(0, 0, 0, 0.08);
}

.primary-card {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(29, 78, 216, 0.05) 100%);
}

.secondary-card {
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 0.1) 0%,
        rgba(22, 163, 74, 0.05) 100%);
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.card-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(248, 250, 252, 0.8) 100%);
}

.card-pattern {
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle at center,
        rgba(59, 130, 246, 0.1) 0%,
        transparent 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
}

.card-header-modern {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    position: relative;
    z-index: 1;
}

.icon-wrapper {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.icon-wrapper.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.icon-wrapper.secondary {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.icon-wrapper svg {
    width: 18px;
    height: 18px;
    color: #ffffff;
}

.card-info {
    flex: 1;
}

.card-title-modern {
    display: block;
    font-size: 13px;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.2;
}

.card-subtitle {
    display: block;
    font-size: 10px;
    color: #64748b;
    margin-top: 1px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 8px;
}

.status-indicator.normal {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
    animation: pulse-status 2s infinite;
}

.status-indicator.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
    animation: pulse-status 1s infinite;
}

.card-content-modern {
    position: relative;
    z-index: 1;
}

.value-display {
    display: flex;
    align-items: baseline;
    margin-bottom: 10px;
}

.main-value {
    font-size: 22px;
    font-weight: 700;
    color: #0f172a;
    line-height: 1;
}

.value-unit {
    font-size: 11px;
    color: #64748b;
    margin-left: 3px;
    font-weight: 500;
}

.trend-indicator {
    display: flex;
    align-items: center;
}

.trend-icon {
    font-size: 12px;
    margin-right: 4px;
}

.trend-text {
    font-size: 10px;
    color: #64748b;
    font-weight: 500;
}

@keyframes pulse-status {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.nav-item.active .nav-indicator {
    opacity: 1;
    transform: translateX(-50%) scale(1.2);
}

/* 通知徽章 */
.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: #ffffff;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slideUp {
    animation: slideUp 0.5s ease-in-out;
}

/* 辐射级别样式 - 参考home.html */
.level-safe {
    border-left: 4px solid #4caf50 !important;
}

.level-warning {
    border-left: 4px solid #ff9800 !important;
}

.level-danger {
    border-left: 4px solid #f44336 !important;
}

/* 天气图标样式 */
.weather-icon svg {
    width: 30px;
    height: 30px;
}

.sunny {
    color: #ff9800;
}

.cloudy {
    color: #90a4ae;
}

.rainy {
    color: #64b5f6;
}

.snowy {
    color: #b0bec5;
}

.safe {
    color: #4caf50;
}

.warning {
    color: #ff9800;
}

.danger {
    color: #f44336;
}

/* 修复在小屏手机上的显示 */
@media screen and (max-width: 360px) {
    .device-card {
        width: calc(100% - 8px);
    }

    .mode-card {
        width: calc(100% - 8px);
    }
}

/* 防止在移动端添加额外的外边距或内边距 */
uni-page-body, uni-page {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
}

.header-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #00b4d8 0%, #0096c7 50%, #8b5cf6 100%);
  border-radius: 32rpx 32rpx 0 0;
}

.device-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 20rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-item.connected {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.status-icon-wrapper {
  position: relative;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon {
  font-size: 24rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.status-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: rgba(16, 185, 129, 0.3);
  animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.3;
  }
}

.status-text {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 600;
}

.battery-status {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 20rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  overflow: hidden;
}

.battery-status.high {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.battery-status.medium {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.battery-status.low {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.battery-icon-wrapper {
  position: relative;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.battery-icon {
  font-size: 24rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.battery-level-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(180deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.6;
}

.battery-text {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 600;
}

.time-wrapper {
  text-align: center;
}

.current-time {
  font-size: 32rpx;
  color: #0f172a;
  font-weight: 700;
  display: block;
  margin-bottom: 4rpx;
}

.time-label {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

/* 超炫主要辐射数据卡片 */
.main-data-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 40rpx;
  padding: 48rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 24rpx 48rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.6);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-data-card.level-safe {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 24rpx 48rpx rgba(16, 185, 129, 0.12);
}

.main-data-card.level-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 0 24rpx 48rpx rgba(245, 158, 11, 0.12);
}

.main-data-card.level-danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(239, 68, 68, 0.4);
  box-shadow: 0 24rpx 48rpx rgba(239, 68, 68, 0.15);
  animation: dangerGlow 3s infinite;
}

@keyframes dangerGlow {
  0%, 100% {
    box-shadow: 0 24rpx 48rpx rgba(239, 68, 68, 0.15);
  }
  50% {
    box-shadow: 0 32rpx 64rpx rgba(239, 68, 68, 0.25);
  }
}

.card-bg-pattern {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: patternRotate 20s linear infinite;
  pointer-events: none;
}

@keyframes patternRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.radiation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 36rpx;
  position: relative;
  z-index: 2;
}

.status-indicator {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.1);
}

.indicator-icon {
  font-size: 48rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
  position: relative;
  z-index: 3;
}

.indicator-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  opacity: 0.6;
  animation: indicatorPulse 3s infinite;
}

.indicator-glow.level-safe {
  background: radial-gradient(circle, rgba(16, 185, 129, 0.3) 0%, transparent 70%);
}

.indicator-glow.level-warning {
  background: radial-gradient(circle, rgba(245, 158, 11, 0.3) 0%, transparent 70%);
}

.indicator-glow.level-danger {
  background: radial-gradient(circle, rgba(239, 68, 68, 0.3) 0%, transparent 70%);
}

@keyframes indicatorPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
}

.indicator-pulse {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  border-radius: 50%;
  border: 2rpx solid;
  animation: ripple 2s infinite;
}

.indicator-pulse.level-safe {
  border-color: rgba(16, 185, 129, 0.4);
}

.indicator-pulse.level-warning {
  border-color: rgba(245, 158, 11, 0.4);
}

.indicator-pulse.level-danger {
  border-color: rgba(239, 68, 68, 0.4);
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

.title-section {
  flex: 1;
  margin-left: 24rpx;
}

.radiation-title {
  font-size: 32rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 8rpx;
  display: block;
}

.radiation-subtitle {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
  display: block;
}

.status-badge {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.status-badge.level-safe {
  background: rgba(16, 185, 129, 0.15);
  border-color: rgba(16, 185, 129, 0.3);
}

.status-badge.level-warning {
  background: rgba(245, 158, 11, 0.15);
  border-color: rgba(245, 158, 11, 0.3);
}

.status-badge.level-danger {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
}

.badge-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #0f172a;
}

.radiation-main {
  text-align: center;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 2;
}

.dose-rate-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.value-container {
  position: relative;
  display: inline-block;
}

.dose-value {
  font-size: 80rpx;
  font-weight: 800;
  color: #0f172a;
  line-height: 1;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.value-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  border-radius: 50%;
  opacity: 0.3;
  animation: valueGlow 4s infinite;
}

.value-glow.level-safe {
  background: radial-gradient(circle, rgba(16, 185, 129, 0.4) 0%, transparent 70%);
}

.value-glow.level-warning {
  background: radial-gradient(circle, rgba(245, 158, 11, 0.4) 0%, transparent 70%);
}

.value-glow.level-danger {
  background: radial-gradient(circle, rgba(239, 68, 68, 0.4) 0%, transparent 70%);
}

@keyframes valueGlow {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
}
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.5;
  }
}

.dose-unit {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 600;
  margin-top: 8rpx;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  border: 1px solid rgba(226, 232, 240, 0.5);
  margin-top: 16rpx;
}

.trend-icon {
  font-size: 24rpx;
}

.trend-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #0f172a;
}

.trend-indicator.up {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.trend-indicator.up .trend-text {
  color: #10b981;
}

.trend-indicator.down {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.trend-indicator.down .trend-text {
  color: #ef4444;
}

.trend-indicator.stable {
  background: rgba(156, 163, 175, 0.1);
  border-color: rgba(156, 163, 175, 0.3);
}

.trend-indicator.stable .trend-text {
  color: #6b7280;
}

.additional-data {
  position: relative;
  z-index: 2;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.data-item {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 24rpx 20rpx;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.data-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.1);
}

.data-item.primary {
  background: linear-gradient(135deg, rgba(0, 180, 216, 0.1) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.data-item.secondary {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.data-item.tertiary {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.data-icon-wrapper {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  margin: 0 auto 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
}

.data-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.icon-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 180, 216, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.data-item:hover .icon-bg {
  opacity: 1;
}

.data-content {
  text-align: center;
}

.data-label {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.data-value {
  font-size: 32rpx;
  color: #0f172a;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 4rpx;
  display: block;
}

.data-unit {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
  display: block;
}

.card-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: cardShine 6s infinite;
  pointer-events: none;
}

@keyframes cardShine {
  0%, 90%, 100% {
    left: -100%;
  }
  10%, 80% {
    left: 100%;
  }
}

/* 精美的快速数据概览 */
.quick-overview {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.overview-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  cursor: pointer;
}

.overview-card:hover {
  transform: translateY(-8rpx);
  box-shadow: 0 24rpx 48rpx rgba(0, 0, 0, 0.12);
}

.overview-card.stats-card {
  background: linear-gradient(135deg, rgba(0, 180, 216, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.overview-card.alert-card {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.overview-card.alert-card.has-alerts {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(239, 68, 68, 0.3);
}

.card-bg-effect {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  transition: transform 0.6s ease;
  pointer-events: none;
}

.overview-card:hover .card-bg-effect {
  transform: scale(1.5) rotate(30deg);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.header-icon-wrapper {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 50%;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
}

.card-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.icon-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: rgba(0, 180, 216, 0.1);
  animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
}

.alert-indicator {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 20rpx;
  height: 20rpx;
  background: #ef4444;
  border-radius: 50%;
  border: 3rpx solid #ffffff;
  animation: alertBlink 1.5s infinite;
}

@keyframes alertBlink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.header-content {
  flex: 1;
  margin-left: 16rpx;
}

.card-title {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 4rpx;
  display: block;
}

.card-subtitle {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  display: block;
}

.header-action {
  padding: 12rpx;
  background: rgba(0, 180, 216, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.action-arrow {
  font-size: 24rpx;
  color: #00b4d8;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.overview-card:hover .action-arrow {
  transform: translateX(4rpx);
}

.alert-badge {
  background: #ef4444;
  color: #ffffff;
  padding: 8rpx 12rpx;
  border-radius: 50%;
  min-width: 40rpx;
  text-align: center;
}

.badge-count {
  font-size: 20rpx;
  font-weight: 700;
}

.card-content {
  margin-bottom: 16rpx;
}

.stats-grid {
  display: grid;
  gap: 16rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.stat-value {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  line-height: 1.2;
}

.stat-unit {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
}

.stat-bar {
  height: 6rpx;
  background: rgba(226, 232, 240, 0.8);
  border-radius: 3rpx;
  overflow: hidden;
  margin-top: 4rpx;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #00b4d8 0%, #0096c7 100%);
  border-radius: 3rpx;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-footer {
  padding-top: 16rpx;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
  text-align: center;
}

.footer-text {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
}

.alert-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.alert-count-display {
  text-align: center;
}

.count-number {
  font-size: 48rpx;
  color: #0f172a;
  font-weight: 800;
  line-height: 1;
  display: block;
}

.count-label {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  margin-top: 4rpx;
  display: block;
}

.alert-status {
  padding: 12rpx 20rpx;
  background: rgba(156, 163, 175, 0.1);
  border-radius: 20rpx;
  border: 1px solid rgba(156, 163, 175, 0.3);
}

.alert-status.active {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.alert-status .status-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #6b7280;
}

.alert-status.active .status-text {
  color: #ef4444;
}

.last-alert {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 16rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.alert-timeline {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.timeline-dot {
  width: 12rpx;
  height: 12rpx;
  background: #ef4444;
  border-radius: 50%;
  box-shadow: 0 0 0 4rpx rgba(239, 68, 68, 0.2);
}

.timeline-line {
  width: 2rpx;
  height: 20rpx;
  background: rgba(239, 68, 68, 0.3);
}

.alert-info {
  flex: 1;
}

.alert-time {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}

.alert-msg {
  font-size: 20rpx;
  color: #0f172a;
  font-weight: 600;
  line-height: 1.3;
}

/* 炫酷的实时图表预览 */
.chart-preview {
  margin-bottom: 32rpx;
}

.chart-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.chart-title-section {
  flex: 1;
}

.chart-title {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 4rpx;
  display: block;
}

.chart-subtitle {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  display: block;
}

.chart-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.action-btn:hover {
  background: rgba(0, 180, 216, 0.1);
  border-color: rgba(0, 180, 216, 0.3);
}

.action-btn.primary {
  background: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);
  border-color: rgba(0, 180, 216, 0.3);
}

.action-btn.primary:hover {
  transform: translateY(-2rpx) scale(1.05);
  box-shadow: 0 8rpx 16rpx rgba(0, 180, 216, 0.3);
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #0f172a;
}

.action-btn.primary .btn-text,
.action-btn.primary .btn-arrow {
  color: #ffffff;
}

.btn-arrow {
  font-size: 18rpx;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.action-btn:hover .btn-arrow {
  transform: translateX(2rpx);
}

.mini-chart-container {
  position: relative;
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  border-radius: 20rpx;
  padding: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.5);
  box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.chart-canvas {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
}

.chart-overlay {
  position: absolute;
  top: 24rpx;
  left: 24rpx;
  right: 24rpx;
  bottom: 56rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4rpx);
  border-radius: 12rpx;
  gap: 16rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(0, 180, 216, 0.2);
  border-left: 4rpx solid #00b4d8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 32rpx;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.legend-dot.primary {
  background: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);
}

.legend-dot.secondary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.legend-text {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

/* 现代化快速操作按钮 */
.quick-actions {
  margin-bottom: 32rpx;
}

.actions-header {
  text-align: center;
  margin-bottom: 24rpx;
}

.actions-title {
  font-size: 32rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 8rpx;
  display: block;
}

.actions-subtitle {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
  display: block;
}

.actions-grid {
  display: grid;
  gap: 20rpx;
}

.action-button {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 28rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.action-button:hover {
  transform: translateY(-6rpx);
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.12);
}

.action-button.primary {
  background: linear-gradient(135deg, rgba(0, 180, 216, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.action-button.primary.active {
  background: linear-gradient(135deg, rgba(0, 180, 216, 0.15) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(0, 180, 216, 0.3);
}

.action-button.secondary {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.action-button.tertiary {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.action-icon-wrapper {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 50%;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.action-icon {
  font-size: 40rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
  position: relative;
  z-index: 2;
}

.icon-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(0, 180, 216, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.action-button:active .icon-ripple {
  width: 100rpx;
  height: 100rpx;
}

.action-content {
  flex: 1;
}

.action-text {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 8rpx;
  display: block;
}

.action-desc {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  display: block;
}

.action-status {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: rgba(156, 163, 175, 0.5);
  transition: all 0.3s ease;
}

.action-status.active {
  background: #10b981;
  box-shadow: 0 0 0 4rpx rgba(16, 185, 129, 0.2);
  animation: statusPulse 2s infinite;
}

/* 炫酷连接状态浮窗 */
.connection-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.overlay-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4rpx);
}

.overlay-content {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 40rpx;
  box-shadow: 0 32rpx 64rpx rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.6);
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.overlay-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.overlay-title {
  font-size: 32rpx;
  color: #0f172a;
  font-weight: 700;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 50%;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s ease;
  cursor: pointer;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.close-icon {
  font-size: 24rpx;
  color: #64748b;
  font-weight: bold;
}

.close-btn:hover .close-icon {
  color: #ef4444;
}

.connection-details {
  margin-bottom: 32rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

.detail-value {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.detail-value.connected .status-dot {
  background: #10b981;
  box-shadow: 0 0 0 4rpx rgba(16, 185, 129, 0.2);
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #ef4444;
  box-shadow: 0 0 0 4rpx rgba(239, 68, 68, 0.2);
}

.value-text {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 600;
}

.battery-indicator {
  width: 80rpx;
  height: 16rpx;
  background: rgba(226, 232, 240, 0.8);
  border-radius: 8rpx;
  overflow: hidden;
}

.battery-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: 8rpx;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.signal-bars {
  display: flex;
  gap: 4rpx;
  align-items: flex-end;
}

.bar {
  width: 8rpx;
  background: rgba(226, 232, 240, 0.8);
  border-radius: 2rpx;
  transition: background 0.3s ease;
}

.bar:nth-child(1) {
  height: 8rpx;
}

.bar:nth-child(2) {
  height: 12rpx;
}

.bar:nth-child(3) {
  height: 16rpx;
}

.bar:nth-child(4) {
  height: 20rpx;
}

.bar.active {
  background: #10b981;
}

.overlay-actions {
  display: flex;
  gap: 16rpx;
}

.overlay-btn {
  flex: 1;
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid;
}

.overlay-btn.secondary {
  background: rgba(248, 250, 252, 0.8);
  border-color: rgba(226, 232, 240, 0.6);
}

.overlay-btn.secondary:hover {
  background: rgba(0, 180, 216, 0.1);
  border-color: rgba(0, 180, 216, 0.3);
}

.overlay-btn.primary {
  background: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);
  border-color: rgba(0, 180, 216, 0.3);
}

.overlay-btn.primary:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 16rpx rgba(0, 180, 216, 0.3);
}

.overlay-btn .btn-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #0f172a;
}

.overlay-btn.primary .btn-text {
  color: #ffffff;
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  bottom: 140rpx;
  right: 40rpx;
  z-index: 100;
}

.fab {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 16rpx 32rpx rgba(139, 92, 246, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 20rpx 40rpx rgba(139, 92, 246, 0.5);
}

.fab-icon {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.fab-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  animation: fabPulse 3s infinite;
}

@keyframes fabPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .dashboard-container {
    padding: 20rpx;
  }
  
  .status-bar {
    padding: 24rpx;
  }
  
  .device-status {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .main-data-card {
    padding: 32rpx;
  }
  
  .data-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .quick-overview {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }
  
  .fab-container {
    bottom: 120rpx;
    right: 30rpx;
  }
  
  .fab {
    width: 100rpx;
    height: 100rpx;
  }
  
  .fab-icon {
    font-size: 40rpx;
  }
}

/* 高级动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.status-bar {
  animation: fadeInUp 0.6s ease-out;
}

.main-data-card {
  animation: scaleIn 0.8s ease-out 0.1s both;
}

.overview-card {
  animation: slideInRight 0.6s ease-out;
}

.overview-card:nth-child(2) {
  animation-delay: 0.1s;
}

.chart-wrapper {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.action-button {
  animation: slideInRight 0.6s ease-out;
}

.action-button:nth-child(2) {
  animation-delay: 0.1s;
}

.action-button:nth-child(3) {
  animation-delay: 0.2s;
}

.fab {
  animation: scaleIn 1s ease-out 0.5s both, fabPulse 3s infinite 1.5s;
}
</style>