<template>
  <view class="charts-container">
    <!-- 顶部状态栏 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <view class="page-title">
          <text class="title-text">数据分析</text>
          <text class="subtitle-text">智能监测 • 实时分析</text>
        </view>
        <view class="refresh-btn" @tap="refreshChart">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
            <path d="M21 3v5h-5"></path>
            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
            <path d="M3 21v-5h5"></path>
          </svg>
        </view>
      </view>
    </view>

    <!-- 时间范围选择器 -->
    <view class="time-range-selector">
      <text class="range-label">时间范围:</text>
      <view class="range-options">
        <view 
          v-for="option in ['1H', '6H', '24H', '7D']" 
          :key="option"
          class="range-option" 
          :class="{ active: selectedTimeRange === option.toLowerCase() }"
          @tap="selectTimeRange(option.toLowerCase())"
        >
          <text>{{ option }}</text>
        </view>
      </view>
    </view>

    <!-- 核心数据卡片 -->
    <view class="data-cards">
      <view class="data-card primary">
        <view class="card-header">
          <view class="card-icon dose-rate">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
            </svg>
          </view>
          <view class="card-title">
            <text class="title">当前剂量率</text>
            <text class="value">{{ radiationState.currentData.doseRate.toFixed(3) }}</text>
            <text class="unit">μSv/h</text>
          </view>
        </view>
        <view class="trend-indicator">
          <text class="trend-icon">{{ doseRateTrend.icon }}</text>
          <text class="trend-text" :class="doseRateTrend.class">{{ doseRateTrend.text }}</text>
        </view>
      </view>

      <view class="data-card secondary">
        <view class="card-header">
          <view class="card-icon cps">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 6v6l4 2"></path>
            </svg>
          </view>
          <view class="card-title">
            <text class="title">计数率</text>
            <text class="value">{{ radiationState.currentData.cps.toFixed(0) }}</text>
            <text class="unit">CPS</text>
          </view>
        </view>
        <view class="trend-indicator">
          <text class="trend-icon">{{ cpsTrend.icon }}</text>
          <text class="trend-text" :class="cpsTrend.class">{{ cpsTrend.text }}</text>
        </view>
      </view>
    </view>

    <!-- 统计概览 -->
    <view class="stats-overview">
      <view class="stats-header">
        <text class="stats-title">今日统计</text>
        <text class="stats-subtitle">{{ selectedTimeRange.toUpperCase() }} 数据概览</text>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-label">最大值</text>
          <text class="stat-value">{{ maxDoseRate.toFixed(3) }}</text>
          <text class="stat-unit">μSv/h</text>
          <view class="stat-bar">
            <view class="stat-fill max" :style="{ width: maxDoseProgress + '%' }"></view>
          </view>
        </view>
        <view class="stat-item">
          <text class="stat-label">平均值</text>
          <text class="stat-value">{{ avgDoseRate.toFixed(3) }}</text>
          <text class="stat-unit">μSv/h</text>
          <view class="stat-bar">
            <view class="stat-fill avg" :style="{ width: avgDoseProgress + '%' }"></view>
          </view>
        </view>
        <view class="stat-item">
          <text class="stat-label">最小值</text>
          <text class="stat-value">{{ minDoseRate.toFixed(3) }}</text>
          <text class="stat-unit">μSv/h</text>
          <view class="stat-bar">
            <view class="stat-fill min" :style="{ width: minDoseProgress + '%' }"></view>
          </view>
        </view>
        <view class="stat-item">
          <text class="stat-label">变化率</text>
          <text class="stat-value">{{ changeRate.toFixed(1) }}</text>
          <text class="stat-unit">%</text>
          <view class="stat-bar">
            <view class="stat-fill change" :style="{ width: Math.abs(changeRate) + '%' }"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 图表容器 -->
    <view class="chart-container">
      <view class="chart-header">
        <text class="chart-title">数据趋势图</text>
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-color dose"></view>
            <text>剂量率</text>
          </view>
          <view class="legend-item">
            <view class="legend-color cps"></view>
            <text>计数率</text>
          </view>
        </view>
      </view>
      <view class="chart-wrapper">
        <canvas 
          class="chart-canvas" 
          canvas-id="radiationChart" 
          :style="{ width: chartWidth + 'px', height: chartHeight + 'px' }"
        ></canvas>
        <view v-if="isLoading" class="chart-loading">
          <view class="loading-spinner"></view>
          <text>加载中...</text>
        </view>
      </view>
    </view>

    <!-- 历史数据列表 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">最新记录</text>
        <text class="view-all" @tap="viewDetailedStats">查看全部</text>
      </view>
      <view class="history-list">
        <view 
          v-for="(item, index) in recentHistory" 
          :key="index"
          class="history-item"
          :class="getStatusClass(item.doseRate)"
        >
          <view class="history-time">
            <text class="time">{{ formatTime(item.timestamp) }}</text>
            <text class="status" :class="getStatusClass(item.doseRate)">{{ getStatusText(item.doseRate) }}</text>
          </view>
          <view class="history-data">
            <text class="dose-rate">{{ item.doseRate.toFixed(3) }} μSv/h</text>
            <text class="cps">{{ item.cps }} CPS</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 浮动操作按钮 -->
    <view class="fab-container">
      <view class="fab main" @tap="toggleFabMenu" :class="{ active: fabMenuOpen }">
        <text class="fab-icon">{{ fabMenuOpen ? '✕' : '🔧' }}</text>
      </view>
      <view class="fab-menu" :class="{ open: fabMenuOpen }">
        <view class="fab-item" @tap="exportData" style="animation-delay: 0.1s">
          <text class="fab-text">导出</text>
          <text class="fab-icon">📊</text>
        </view>
        <view class="fab-item" @tap="shareChart" style="animation-delay: 0.2s">
          <text class="fab-text">分享</text>
          <text class="fab-icon">📤</text>
        </view>
        <view class="fab-item" @tap="resetChart" style="animation-delay: 0.3s">
          <text class="fab-text">重置</text>
          <text class="fab-icon">🔄</text>
        </view>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <BottomNavigation currentPage="charts" />
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState } from '../../utils/dataStore.js'
import ToastContainer from '../../components/ToastContainer.vue'
import BottomNavigation from '../../components/BottomNavigation.vue'
import toastManager from '../../utils/toastManager.js'

export default {
  name: 'Charts',
  components: {
    ToastContainer,
    BottomNavigation
  },
  setup() {
    const selectedTimeRange = ref('24h')
    const isLoading = ref(false)
    const chartWidth = ref(300)
    const chartHeight = ref(300)  // 增加图表高度以匹配CSS
    const fabMenuOpen = ref(false)
    let chartContext = null
    let updateInterval = null

    const doseRateTrend = computed(() => {
      const history = radiationState.history.slice(0, 10)
      if (history.length < 2) return { icon: '➖', text: '无数据', class: 'neutral' }
      
      const current = history[0].doseRate
      const previous = history[1].doseRate
      const change = ((current - previous) / previous * 100)
      
      if (Math.abs(change) < 1) return { icon: '➖', text: '稳定', class: 'stable' }
      if (change > 0) return { icon: '📈', text: `+${change.toFixed(1)}%`, class: 'up' }
      return { icon: '📉', text: `${change.toFixed(1)}%`, class: 'down' }
    })

    const cpsTrend = computed(() => {
      const history = radiationState.history.slice(0, 10)
      if (history.length < 2) return { icon: '➖', text: '无数据', class: 'neutral' }
      
      const current = history[0].cps
      const previous = history[1].cps
      const change = ((current - previous) / previous * 100)
      
      if (Math.abs(change) < 1) return { icon: '➖', text: '稳定', class: 'stable' }
      if (change > 0) return { icon: '📈', text: `+${change.toFixed(1)}%`, class: 'up' }
      return { icon: '📉', text: `${change.toFixed(1)}%`, class: 'down' }
    })

    const todayStats = computed(() => {
      const today = new Date().toDateString()
      const todayData = radiationState.history.filter(item => 
        new Date(item.timestamp).toDateString() === today
      )

      if (todayData.length === 0) {
        return { avgDoseRate: 0, maxDoseRate: 0, totalDose: 0 }
      }

      const doseRates = todayData.map(item => item.doseRate)
      return {
        avgDoseRate: doseRates.reduce((sum, rate) => sum + rate, 0) / doseRates.length,
        maxDoseRate: Math.max(...doseRates),
        totalDose: radiationState.currentData.doseSum
      }
    })

    const todayAlerts = computed(() => {
      const today = new Date().toDateString()
      return radiationState.alerts.filter(alert => 
        new Date(alert.timestamp).toDateString() === today
      )
    })

    const recentHistory = computed(() => {
      return radiationState.history.slice(0, 10)
    })

    // 方法
    const selectTimeRange = (range) => {
      selectedTimeRange.value = range
      drawChart()
    }

    const refreshChart = () => {
      isLoading.value = true
      setTimeout(() => {
        drawChart()
        isLoading.value = false
      }, 1000)
    }

    const exportChart = () => {
      uni.canvasToTempFilePath({
        canvasId: 'radiationChart',
        success: (res) => {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              toastManager.success('图表已保存')
            }
          })
        }
      })
    }

    const exportData = () => {
      uni.showActionSheet({
        itemList: ['导出CSV数据', '导出JSON数据', '保存图表图片'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              exportCSV()
              break
            case 1:
              exportJSON()
              break
            case 2:
              exportChart()
              break
          }
        }
      })
    }

    const exportCSV = () => {
      const data = radiationState.history.slice(0, 100)
      let csvContent = '时间,剂量率(μSv/h),计数率(CPS),温度(°C)\n'
      
      data.forEach(item => {
        const time = new Date(item.timestamp).toLocaleString()
        csvContent += `${time},${item.doseRate},${item.cps},${item.temperature}\n`
      })
      
              toastManager.success('CSV数据已准备')
            }

    const exportJSON = () => {
      const exportData = {
        exportTime: new Date().toISOString(),
        totalRecords: radiationState.history.length,
        data: radiationState.history.slice(0, 100)
      }

      console.log('导出数据:', exportData)
      toastManager.success('JSON数据已准备')
    }

    const goBack = () => {
      uni.navigateBack()
    }

    // 统一的导航函数（支持tabBar与普通页面）
    const navigateTo = (page) => {
      const routes = {
        'dashboard': '/pages/dashboard/dashboard',
        'charts': '/pages/charts/charts',
        'health': '/pages/health/health',
        'map': '/pages/map/map',
        'settings': '/pages/settings/settings',
        'notification': '/pages/notification/notification'
      }

      if (routes[page]) {
        // 使用navigateTo替代switchTab，因为我们移除了tabBar配置
        uni.navigateTo({
          url: routes[page],
          fail: (err) => {
            console.error('导航失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      }
    }

    const getAlertCount = (type) => {
      return todayAlerts.value.filter(alert => alert.type === type).length
    }

    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const getStatusClass = (doseRate) => {
      if (doseRate > 1.0) return 'high'
      if (doseRate > 0.5) return 'warning'
      return 'normal'
    }

    const getStatusText = (doseRate) => {
      if (doseRate > 1.0) return '高'
      if (doseRate > 0.5) return '中'
      return '正常'
    }

    const initChart = () => {
      chartContext = uni.createCanvasContext('radiationChart')
      if (chartContext) {
        drawChart()
        // 设置定时更新
        updateInterval = setInterval(drawChart, 5000)
      }
    }

    const drawChart = () => {
      if (!chartContext) return
      
      const ctx = chartContext
      const width = chartWidth.value
      const height = chartHeight.value
      const padding = 40
      
      // 清空画布
      ctx.clearRect(0, 0, width, height)
      
      // 设置画布背景渐变
      const bgGradient = ctx.createLinearGradient(0, 0, 0, height)
      bgGradient.addColorStop(0, '#fefefe')
      bgGradient.addColorStop(1, '#f8fafc')
      ctx.setFillStyle(bgGradient)
      ctx.fillRect(0, 0, width, height)
      
      // 获取数据 - 扩大显示范围
      let dataCount = 50  // 增加默认数据点
      switch (selectedTimeRange.value) {
        case '1h': dataCount = 30; break    // 增加数据点
        case '24h': dataCount = 60; break   // 增加数据点
        case '7d': dataCount = 168; break   // 7天每小时一个点
      }
      
      const data = radiationState.history.slice(-dataCount)  // 获取最新数据
      if (data.length < 2) {
        // 绘制无数据提示
        ctx.setFillStyle('#64748b')
        ctx.setFontSize(16)
        ctx.setTextAlign('center')
        ctx.fillText('暂无数据', width / 2, height / 2)
        ctx.draw()
        return
      }
      
      // 绘制网格线 - 更美观的样式
      ctx.setStrokeStyle('rgba(226, 232, 240, 0.4)')
      ctx.setLineWidth(0.8)
      
      // 垂直网格线
      for (let i = 1; i < 10; i++) {  // 去掉边界线
        const x = padding + (i / 10) * (width - padding * 2)
        ctx.beginPath()
        ctx.moveTo(x, padding)
        ctx.lineTo(x, height - padding)
        ctx.stroke()
      }
      
      // 水平网格线
      for (let i = 1; i < 5; i++) {  // 去掉边界线
        const y = padding + (i / 5) * (height - padding * 2)
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }
      
      // 绘制坐标轴
      ctx.setStrokeStyle('#64748b')
      ctx.setLineWidth(2)
      
      // X轴
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)
      ctx.lineTo(width - padding, height - padding)
      ctx.stroke()
      
      // Y轴
      ctx.beginPath()
      ctx.moveTo(padding, padding)
      ctx.lineTo(padding, height - padding)
      ctx.stroke()
      
      // 绘制剂量率曲线
      const doseRates = data.map(item => item.doseRate)
      const maxDose = Math.max(...doseRates)
      const minDose = Math.min(...doseRates)
      const doseRange = (maxDose - minDose) * 1.2 || 1  // 增加显示范围
      const adjustedMinDose = minDose - (maxDose - minDose) * 0.1
      
      // 计算坐标点
      const dosePoints = data.map((item, index) => ({
        x: padding + (index / (data.length - 1)) * (width - padding * 2),
        y: height - padding - ((item.doseRate - adjustedMinDose) / doseRange) * (height - padding * 2)
      }))
      
      // 绘制剂量率渐变填充
      if (dosePoints.length > 1) {
        const doseGradient = ctx.createLinearGradient(0, padding, 0, height - padding)
        doseGradient.addColorStop(0, 'rgba(0, 180, 216, 0.4)')
        doseGradient.addColorStop(0.5, 'rgba(0, 180, 216, 0.2)')
        doseGradient.addColorStop(1, 'rgba(0, 180, 216, 0.05)')
        ctx.setFillStyle(doseGradient)
        
        ctx.beginPath()
        ctx.moveTo(dosePoints[0].x, height - padding)
        dosePoints.forEach((point, index) => {
        if (index === 0) {
            ctx.lineTo(point.x, point.y)
        } else {
            // 平滑曲线
            const prevPoint = dosePoints[index - 1]
            const cpx = (prevPoint.x + point.x) / 2
            ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
          }
        })
        ctx.lineTo(dosePoints[dosePoints.length - 1].x, height - padding)
        ctx.closePath()
        ctx.fill()
      }
      
      // 绘制剂量率曲线
      ctx.beginPath()
      ctx.setStrokeStyle('#00b4d8')
      ctx.setLineWidth(4)
      ctx.setShadow(0, 3, 8, 'rgba(0, 180, 216, 0.3)')
      
      dosePoints.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y)
        } else {
          // 平滑曲线
          const prevPoint = dosePoints[index - 1]
          const cpx = (prevPoint.x + point.x) / 2
          ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
        }
      })
      ctx.stroke()
      
      // 绘制计数率曲线（缩放显示）
      const cpsData = data.map(item => item.cps)
      const maxCps = Math.max(...cpsData)
      const minCps = Math.min(...cpsData)
      const cpsRange = (maxCps - minCps) * 1.2 || 1
      const adjustedMinCps = minCps - (maxCps - minCps) * 0.1
      
      // 计算CPS坐标点
      const cpsPoints = data.map((item, index) => ({
        x: padding + (index / (data.length - 1)) * (width - padding * 2),
        y: height - padding - ((item.cps - adjustedMinCps) / cpsRange) * (height - padding * 2) * 0.6  // 占60%高度
      }))
      
      // 绘制CPS渐变填充
      if (cpsPoints.length > 1) {
        const cpsGradient = ctx.createLinearGradient(0, height - padding, 0, height - padding - (height - padding * 2) * 0.6)
        cpsGradient.addColorStop(0, 'rgba(16, 185, 129, 0.3)')
        cpsGradient.addColorStop(1, 'rgba(16, 185, 129, 0.05)')
        ctx.setFillStyle(cpsGradient)
        
      ctx.beginPath()
        ctx.moveTo(cpsPoints[0].x, height - padding)
        cpsPoints.forEach((point, index) => {
        if (index === 0) {
            ctx.lineTo(point.x, point.y)
        } else {
            const prevPoint = cpsPoints[index - 1]
            const cpx = (prevPoint.x + point.x) / 2
            ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
          }
        })
        ctx.lineTo(cpsPoints[cpsPoints.length - 1].x, height - padding)
        ctx.closePath()
        ctx.fill()
      }
      
      // 绘制CPS曲线
      ctx.beginPath()
      ctx.setStrokeStyle('#10b981')
      ctx.setLineWidth(3)
      ctx.setShadow(0, 2, 6, 'rgba(16, 185, 129, 0.3)')
      
      cpsPoints.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y)
        } else {
          const prevPoint = cpsPoints[index - 1]
          const cpx = (prevPoint.x + point.x) / 2
          ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
        }
      })
      ctx.stroke()
      
      // 重置阴影
      ctx.setShadow(0, 0, 0, 'transparent')
      
      // 绘制数据点（仅显示关键点）
      const showPointInterval = Math.max(1, Math.floor(data.length / 20))  // 最多显示20个点
      dosePoints.forEach((point, index) => {
        if (index % showPointInterval === 0 || index === dosePoints.length - 1) {
          // 外圈
          ctx.beginPath()
          ctx.setFillStyle('#ffffff')
          ctx.arc(point.x, point.y, 5, 0, 2 * Math.PI)
          ctx.fill()
          // 内圈
          ctx.beginPath()
          ctx.setFillStyle('#00b4d8')
          ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI)
          ctx.fill()
        }
      })
      
      // CPS数据点
      cpsPoints.forEach((point, index) => {
        if (index % showPointInterval === 0 || index === cpsPoints.length - 1) {
          ctx.beginPath()
          ctx.setFillStyle('#ffffff')
          ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI)
          ctx.fill()
          ctx.beginPath()
          ctx.setFillStyle('#10b981')
          ctx.arc(point.x, point.y, 2.5, 0, 2 * Math.PI)
          ctx.fill()
        }
      })
      
      ctx.draw()
    }

    onMounted(() => {
      setTimeout(() => {
        initChart()
      }, 500)
    })

      // 新增的交互方法
    const focusOnDoseRate = () => {
      toastManager.info('聚焦剂量率数据')
    }

    const focusOnCumulative = () => {
      toastManager.info('聚焦累积剂量')
    }

    const focusOnCPS = () => {
      toastManager.info('聚焦计数率数据')
    }

    const openChartSettings = () => {
      uni.showActionSheet({
        itemList: ['切换主题', '调整精度', '数据源设置'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              toastManager.info('主题切换')
              break
            case 1:
              toastManager.info('精度调整')
              break
            case 2:
              toastManager.info('数据源设置')
              break
          }
        }
      })
    }

    const viewDetailedStats = () => {
      uni.navigateTo({
        url: '/pages/stats/stats'
      })
    }

    const toggleFabMenu = () => {
      fabMenuOpen.value = !fabMenuOpen.value
    }

    const shareChart = () => {
      uni.share({
        provider: 'weixin',
        type: 1,
        title: '辐射监测数据图表',
        summary: '智能辐射监测系统生成的数据分析图表',
        success: () => {
          toastManager.success('分享成功')
        }
      })
    }

    const resetChart = () => {
      selectedTimeRange.value = '24h'
      drawChart()
      toastManager.success('视图已重置')
    }

      onUnmounted(() => {
      if (updateInterval) {
        clearInterval(updateInterval)
      }
    })

    return {
      radiationState,
      selectedTimeRange,
      isLoading,
      chartWidth,
      chartHeight,
      doseRateTrend,
      cpsTrend,
      todayStats,
      todayAlerts,
      recentHistory,
      selectTimeRange,
      refreshChart,
      exportChart,
      exportData,
      getAlertCount,
      formatTime,
      getStatusClass,
      getStatusText,
      fabMenuOpen,
      focusOnDoseRate,
      focusOnCumulative,
      focusOnCPS,
      openChartSettings,
      viewDetailedStats,
      toggleFabMenu,
      shareChart,
      resetChart,
      maxDoseRate: computed(() => Math.max(...radiationState.history.slice(0, 50).map(item => item.doseRate))),
      minDoseRate: computed(() => Math.min(...radiationState.history.slice(0, 50).map(item => item.doseRate))),
      avgDoseRate: computed(() => {
        const data = radiationState.history.slice(0, 50)
        return data.reduce((sum, item) => sum + item.doseRate, 0) / data.length || 0
      }),
      changeRate: computed(() => {
        const recent = radiationState.history.slice(0, 10)
        if (recent.length < 2) return 0
        const current = recent[0].doseRate
        const previous = recent[recent.length - 1].doseRate
        return ((current - previous) / previous) * 100
      }),
      maxDoseProgress: computed(() => 100),
      minDoseProgress: computed(() => 50),
      avgDoseProgress: computed(() => 75),
      goBack,
      navigateTo
    }
  }
}
</script>

<style scoped>
/* 全局容器样式 */
.charts-container {
  min-height: 100vh;
  background: #ffffff;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
  padding-bottom: 120px;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

/* 状态栏 */
.status-bar {
  height: 44px;
  background: transparent;
}

/* 页面头部 */
.page-header {
  padding: 20px 16px 30px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 0 0 24px 24px;
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  flex: 1;
}

.title-text {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
}

.subtitle-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.refresh-btn {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.refresh-btn svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
}

.refresh-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 时间范围选择器 */
.time-range-selector {
  margin: 0 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.range-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 12px;
  display: block;
}

.range-options {
  display: flex;
  gap: 8px;
}

.range-option {
  flex: 1;
  padding: 12px;
  background: #f8fafc;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.range-option.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #1d4ed8;
  transform: translateY(-2px);
}

.range-option text {
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
}

.range-option.active text {
  color: #ffffff;
}

/* 核心数据卡片 */
.data-cards {
  display: flex;
  gap: 12px;
  margin: 0 16px 20px;
}

.data-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.data-card.primary {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.05) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.data-card.secondary {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon.dose-rate {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.card-icon.cps {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.card-icon svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
}

.card-title {
  flex: 1;
}

.card-title .title {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
}

.card-title .value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 2px;
}

.card-title .unit {
  font-size: 12px;
  color: #94a3b8;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 20px;
}

.trend-icon {
  font-size: 14px;
}

.trend-text {
  font-size: 12px;
  font-weight: 600;
}

.trend-text.up { color: #ef4444; }
.trend-text.down { color: #10b981; }
.trend-text.stable { color: #64748b; }

/* 统计概览 */
.stats-overview {
  margin: 0 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stats-header {
  margin-bottom: 20px;
}

.stats-title {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 4px;
}

.stats-subtitle {
  font-size: 14px;
  color: #64748b;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 4px;
}

.stat-unit {
  font-size: 12px;
  color: #94a3b8;
  margin-bottom: 12px;
  display: block;
}

.stat-bar {
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.stat-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.6s ease;
}

.stat-fill.max { background: linear-gradient(90deg, #ef4444, #dc2626); }
.stat-fill.avg { background: linear-gradient(90deg, #3b82f6, #1d4ed8); }
.stat-fill.min { background: linear-gradient(90deg, #10b981, #059669); }
.stat-fill.change { background: linear-gradient(90deg, #f59e0b, #d97706); }

/* 图表容器 */
.chart-container {
  margin: 0 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 6px;
}

.legend-color.dose {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.legend-color.cps {
  background: linear-gradient(135deg, #10b981, #059669);
}

.legend-item text {
  font-size: 12px;
  color: #64748b;
}

.chart-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: #f8fafc;
  border-radius: 12px;
  overflow: hidden;
}

.chart-canvas {
  border-radius: 12px;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-loading text {
  font-size: 14px;
  color: #64748b;
}

/* 历史数据列表 */
.history-section {
  margin: 0 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
}

.view-all {
  font-size: 14px;
  color: #3b82f6;
  font-weight: 600;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  border-left: 4px solid #e2e8f0;
}

.history-item.normal {
  border-left-color: #10b981;
}

.history-item.warning {
  border-left-color: #f59e0b;
}

.history-item.danger {
  border-left-color: #ef4444;
}

.history-time {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-time .time {
  font-size: 14px;
  color: #0f172a;
  font-weight: 600;
}

.history-time .status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 600;
}

.status.normal {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.status.danger {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.history-data {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.history-data .dose-rate {
  font-size: 16px;
  font-weight: 700;
  color: #0f172a;
}

.history-data .cps {
  font-size: 12px;
  color: #64748b;
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  bottom: 140px;
  right: 20px;
  z-index: 100;
}

.fab {
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.fab.active {
  transform: rotate(45deg);
}

.fab-icon {
  font-size: 24px;
  color: #ffffff;
}

.fab-menu {
  position: absolute;
  bottom: 70px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.fab-menu.open {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.fab-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 12px 16px;
  border-radius: 28px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateX(100px);
  animation: slideInRight 0.3s ease forwards;
}

.fab-text {
  font-size: 14px;
  color: #0f172a;
  font-weight: 600;
  white-space: nowrap;
}

.fab-item .fab-icon {
  font-size: 16px;
  color: #3b82f6;
}

@keyframes slideInRight {
  to {
    transform: translateX(0);
  }
}

/* 全局统一底部导航栏样式 */
.modern-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 0 20px 20px;
}

.nav-background {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 30%,
    rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.nav-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24px;
  padding: 12px 16px;
  margin: 0 8px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.nav-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  border-radius: 16px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  min-width: 60px;
}

.nav-tab.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow:
    0 8px 24px rgba(59, 130, 246, 0.3),
    0 4px 12px rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.nav-tab:hover:not(.active) {
  background: rgba(59, 130, 246, 0.08);
  transform: translateY(-1px);
}

.tab-icon-container {
  position: relative;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.tab-icon {
  width: 22px;
  height: 22px;
  color: #64748b;
  transition: all 0.3s ease;
}

.nav-tab.active .tab-icon {
  color: #ffffff;
  transform: scale(1.1);
}

.tab-label {
  font-size: 11px;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
}

.nav-tab.active .tab-label {
  color: #ffffff;
  font-weight: 600;
}

.notification-dot {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

/* 响应式优化 */
@media (max-width: 375px) {
  .data-cards {
    flex-direction: column;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .range-options {
    flex-wrap: wrap;
  }
}

/* 页面动画 */
.charts-container > * {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header { animation-delay: 0.1s; }
.time-range-selector { animation-delay: 0.2s; }
.data-cards { animation-delay: 0.3s; }
.stats-overview { animation-delay: 0.4s; }
.chart-container { animation-delay: 0.5s; }
.history-section { animation-delay: 0.6s; }
</style> 